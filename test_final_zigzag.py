#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataclasses import dataclass
from decimal import Decimal, getcontext
from typing import Optional, List
from datetime import datetime, timedelta
import random

getcontext().prec = 28

@dataclass
class StockData:
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None
    trend_status: Optional[str] = None

@dataclass
class ZigZagPoint:
    date: str
    point_type: str
    price: Decimal
    source: str = "zz"

@dataclass
class ZigZagState:
    symbol: str
    atr_period: int = 14
    atr_multiplier: Decimal = Decimal('1.5')
    min_price_move: Decimal = Decimal('0')
    min_trend_bars: int = 1
    prev_close: Optional[Decimal] = None
    atr: Optional[Decimal] = None
    atr_count: int = 0
    tr_sum: Decimal = Decimal('0')
    trend: Optional[str] = None
    bars_in_trend: int = 0
    extreme_high: Optional[Decimal] = None
    extreme_high_date: Optional[str] = None
    extreme_low: Optional[Decimal] = None
    extreme_low_date: Optional[str] = None
    confirmed_points: List[ZigZagPoint] = None
    pending_point: Optional[ZigZagPoint] = None

    def __post_init__(self):
        if self.confirmed_points is None:
            self.confirmed_points = []

# 复制优化后的函数（避免导入main2.py时的网络请求）

def _update_atr(state: ZigZagState, bar: StockData):
    high, low, close = bar.high, bar.low, bar.close
    if state.prev_close is None:
        tr = high - low
    else:
        tr = max(high - low, abs(high - state.prev_close), abs(low - state.prev_close))

    if state.atr_count < state.atr_period:
        state.tr_sum += tr
        state.atr_count += 1
        if state.atr_count == state.atr_period:
            state.atr = (state.tr_sum / Decimal(state.atr_period))
    else:
        state.atr = ((state.atr * Decimal(state.atr_period - 1)) + tr) / Decimal(state.atr_period)

    state.prev_close = close

def _is_local_extreme(stock_data_list: List[StockData], index: int, point_type: str, window: int = 3) -> bool:
    """验证指定索引的点是否为局部极值"""
    if index < 0 or index >= len(stock_data_list):
        return False

    start_idx = max(0, index - window)
    end_idx = min(len(stock_data_list), index + window + 1)

    if point_type == "HIGH":
        window_highs = [stock_data_list[j].high for j in range(start_idx, end_idx)]
        return stock_data_list[index].high == max(window_highs)
    else:  # LOW
        window_lows = [stock_data_list[j].low for j in range(start_idx, end_idx)]
        return stock_data_list[index].low == min(window_lows)

def _update_trend_status_from_point(stock_data_list: List[StockData], from_date: str, trend: str, symbol: str):
    """从指定日期开始更新趋势状态"""
    if not stock_data_list:
        return

    start_idx = -1
    for i in range(len(stock_data_list) - 1, -1, -1):
        stock_date_str = stock_data_list[i].date.strftime('%Y-%m-%d')
        if stock_date_str == from_date and stock_data_list[i].symbol == symbol:
            start_idx = i
            break

    if start_idx >= 0:
        for i in range(start_idx, len(stock_data_list)):
            if stock_data_list[i].symbol == symbol:
                stock_data_list[i].trend_status = trend

def _clear_zigzag_point_marking(stock_data_list: List[StockData], date: str, symbol: str):
    """清除指定日期的zigzag点标记"""
    for stock_data in stock_data_list:
        stock_date_str = stock_data.date.strftime('%Y-%m-%d')
        if stock_date_str == date and stock_data.symbol == symbol:
            stock_data.is_zigzag_point = False
            stock_data.zigzag_point_type = None
            break

def _confirm_point(state: ZigZagState, point_type: str, date: str, price: Decimal, stock_data_list: List[StockData] = None):
    """确认拐点，避免连续的高点或低点，确保真正的局部极值"""
    # 首先验证是否为局部极值
    if stock_data_list:
        target_idx = -1
        for i, stock_data in enumerate(stock_data_list):
            stock_date_str = stock_data.date.strftime('%Y-%m-%d')
            if stock_date_str == date and stock_data.symbol == state.symbol:
                target_idx = i
                break

        # 验证局部极值
        if target_idx >= 0 and not _is_local_extreme(stock_data_list, target_idx, point_type, window=5):
            return  # 不是局部极值，跳过

    # 检查连续同类型点
    if state.confirmed_points and state.confirmed_points[-1].point_type == point_type:
        last = state.confirmed_points[-1]
        should_replace = False
        if point_type == "HIGH":
            should_replace = price > last.price
        else:
            should_replace = price < last.price

        if should_replace:
            if stock_data_list:
                _clear_zigzag_point_marking(stock_data_list, last.date, state.symbol)
            state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
        else:
            return
    else:
        state.confirmed_points.append(ZigZagPoint(date, point_type, price))

    # 更新StockData对象
    if stock_data_list:
        for stock_data in stock_data_list:
            stock_date_str = stock_data.date.strftime('%Y-%m-%d')
            if stock_date_str == date and stock_data.symbol == state.symbol:
                stock_data.is_zigzag_point = True
                stock_data.zigzag_point_type = point_type
                break

        # 更新趋势状态
        if len(state.confirmed_points) >= 2:
            last_two = state.confirmed_points[-2:]
            current_trend = None
            if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
                current_trend = "UP"
            elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
                current_trend = "DOWN"

            if current_trend:
                _update_trend_status_from_point(stock_data_list, date, current_trend, state.symbol)

    state.pending_point = None

def update_state(state: ZigZagState, bar: StockData, stock_data_list: List[StockData] = None) -> ZigZagState:
    _update_atr(state, bar)

    if state.atr is None:
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')
        if state.pending_point is None:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
                state.trend = "DOWN"
            else:
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
                state.trend = "UP"
        state.bars_in_trend += 1
        return state

    if state.trend is None:
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')

        rng = state.extreme_high - state.extreme_low
        if rng >= state.atr * state.atr_multiplier and rng >= state.min_price_move:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.trend = "DOWN"
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
            else:
                state.trend = "UP"
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
            state.bars_in_trend = 1
        else:
            state.bars_in_trend += 1
        return state

    state.bars_in_trend += 1

    if state.trend == "UP":
        if state.extreme_high is None or bar.high >= state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

        if (state.extreme_high - bar.low) >= state.atr * state.atr_multiplier and \
           (state.extreme_high - bar.low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            _confirm_point(state, "HIGH", state.extreme_high_date, state.extreme_high, stock_data_list)
            state.trend = "DOWN"
            state.bars_in_trend = 1
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

    elif state.trend == "DOWN":
        if state.extreme_low is None or bar.low <= state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

        if (bar.high - state.extreme_low) >= state.atr * state.atr_multiplier and \
           (bar.high - state.extreme_low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            _confirm_point(state, "LOW", state.extreme_low_date, state.extreme_low, stock_data_list)
            state.trend = "UP"
            state.bars_in_trend = 1
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

    return state

def cleanup_invalid_zigzag_points(stock_data_list: List[StockData], state: ZigZagState, window: int = 5):
    """清理不符合局部极值条件的ZigZag点"""
    if not stock_data_list:
        return

    points_to_remove = []

    for i, data in enumerate(stock_data_list):
        if data.is_zigzag_point and data.symbol == state.symbol:
            if not _is_local_extreme(stock_data_list, i, data.zigzag_point_type, window):
                points_to_remove.append((i, data.date.strftime('%Y-%m-%d'), data.zigzag_point_type))
                data.is_zigzag_point = False
                data.zigzag_point_type = None

    if points_to_remove:
        print(f"清理 {len(points_to_remove)} 个非局部极值点:")
        for idx, date_str, point_type in points_to_remove:
            print(f"  移除索引{idx} {date_str} {point_type}点")
            state.confirmed_points = [p for p in state.confirmed_points
                                    if not (p.date == date_str and p.point_type == point_type)]

    # 重新验证序列
    if len(state.confirmed_points) > 1:
        cleaned_points = [state.confirmed_points[0]]
        for i in range(1, len(state.confirmed_points)):
            current = state.confirmed_points[i]
            last = cleaned_points[-1]

            if current.point_type != last.point_type:
                cleaned_points.append(current)
            else:
                if current.point_type == "HIGH":
                    if current.price > last.price:
                        _clear_zigzag_point_marking(stock_data_list, last.date, state.symbol)
                        cleaned_points[-1] = current
                    else:
                        _clear_zigzag_point_marking(stock_data_list, current.date, state.symbol)
                else:
                    if current.price < last.price:
                        _clear_zigzag_point_marking(stock_data_list, last.date, state.symbol)
                        cleaned_points[-1] = current
                    else:
                        _clear_zigzag_point_marking(stock_data_list, current.date, state.symbol)

        state.confirmed_points = cleaned_points

def create_realistic_test_data(days: int = 50):
    """创建更真实的测试数据，包含噪音和假突破"""
    base_date = datetime(2025, 7, 1)
    stock_data_list = []
    
    # 基础价格走势：上升-下降-上升
    base_price = 30.0
    trend_changes = [
        (0, 15, 0.1),    # 前15天上升趋势，每天平均上涨0.1
        (15, 35, -0.08), # 中间20天下降趋势，每天平均下跌0.08
        (35, days, 0.06) # 最后上升趋势，每天平均上涨0.06
    ]
    
    prices = []
    current_price = base_price
    
    for day in range(days):
        # 确定当前趋势
        trend_rate = 0
        for start, end, rate in trend_changes:
            if start <= day < end:
                trend_rate = rate
                break
        
        # 添加随机噪音
        noise = random.uniform(-0.05, 0.05)
        daily_change = trend_rate + noise
        
        # 计算OHLC
        open_price = current_price
        close_price = current_price * (1 + daily_change)
        
        # 添加日内波动
        intraday_range = abs(daily_change) * 2 + 0.02
        high_price = max(open_price, close_price) * (1 + random.uniform(0, intraday_range))
        low_price = min(open_price, close_price) * (1 - random.uniform(0, intraday_range))
        
        prices.append((open_price, high_price, low_price, close_price))
        current_price = close_price
    
    # 创建StockData对象
    for i, (open_price, high_price, low_price, close_price) in enumerate(prices):
        date = base_date + timedelta(days=i)
        stock_data = StockData(
            symbol="TEST001",
            date=date,
            open=Decimal(f"{open_price:.2f}"),
            high=Decimal(f"{high_price:.2f}"),
            low=Decimal(f"{low_price:.2f}"),
            close=Decimal(f"{close_price:.2f}"),
            volume=random.randint(800000, 1200000)
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list

def validate_zigzag_points(data: List[StockData], window: int = 5):
    """验证ZigZag点是否确实是局部极值"""
    print(f"\n=== 验证ZigZag点（窗口大小={window}） ===")
    
    valid_count = 0
    invalid_count = 0
    
    for i, point in enumerate(data):
        if point.is_zigzag_point:
            start_idx = max(0, i - window)
            end_idx = min(len(data), i + window + 1)
            
            if point.zigzag_point_type == 'HIGH':
                window_highs = [data[j].high for j in range(start_idx, end_idx)]
                is_valid = point.high == max(window_highs)
                status = '✓' if is_valid else '✗'
                print(f"索引{i} HIGH点 {point.high}: {status} (窗口{start_idx}-{end_idx-1}最高{max(window_highs)})")
                if is_valid:
                    valid_count += 1
                else:
                    invalid_count += 1
                    
            elif point.zigzag_point_type == 'LOW':
                window_lows = [data[j].low for j in range(start_idx, end_idx)]
                is_valid = point.low == min(window_lows)
                status = '✓' if is_valid else '✗'
                print(f"索引{i} LOW点 {point.low}: {status} (窗口{start_idx}-{end_idx-1}最低{min(window_lows)})")
                if is_valid:
                    valid_count += 1
                else:
                    invalid_count += 1
    
    total = valid_count + invalid_count
    if total > 0:
        print(f"\n验证结果: {valid_count}/{total} ({valid_count/total*100:.1f}%) 有效")
        return valid_count / total
    else:
        print("没有找到ZigZag点")
        return 0

def check_consecutive_points(state: ZigZagState):
    """检查是否有连续的同类型点"""
    consecutive_count = 0
    if len(state.confirmed_points) > 1:
        for i in range(1, len(state.confirmed_points)):
            if state.confirmed_points[i].point_type == state.confirmed_points[i-1].point_type:
                consecutive_count += 1
                print(f"⚠️  连续{state.confirmed_points[i].point_type}点: "
                      f"{state.confirmed_points[i-1].date} -> {state.confirmed_points[i].date}")
    
    if consecutive_count == 0:
        print("✅ 没有连续的同类型点")
    else:
        print(f"❌ 发现 {consecutive_count} 个连续同类型点")
    
    return consecutive_count == 0

if __name__ == "__main__":
    print("=== 测试优化后的ZigZag算法 ===")
    
    # 设置随机种子以获得可重复的结果
    random.seed(42)
    
    # 创建测试数据
    stock_data_list = create_realistic_test_data(50)
    print(f"创建了 {len(stock_data_list)} 天的测试数据")
    
    # 初始化状态
    state = ZigZagState(symbol="TEST001", atr_multiplier=Decimal('1.2'))
    
    print("\n=== 处理数据 ===")
    for i, bar in enumerate(stock_data_list):
        state = update_state(state, bar, stock_data_list)
    
    print(f"\n处理完成，共确认 {len(state.confirmed_points)} 个拐点")
    
    # 验证处理前的状态
    print("\n=== 清理前验证 ===")
    validity_before = validate_zigzag_points(stock_data_list)
    consecutive_ok_before = check_consecutive_points(state)
    
    # 执行清理
    print("\n=== 执行清理 ===")
    cleanup_invalid_zigzag_points(stock_data_list, state)
    
    # 验证清理后的状态
    print("\n=== 清理后验证 ===")
    validity_after = validate_zigzag_points(stock_data_list)
    consecutive_ok_after = check_consecutive_points(state)
    
    print(f"\n=== 优化效果 ===")
    print(f"局部极值有效性: {validity_before:.1%} -> {validity_after:.1%}")
    print(f"连续点问题: {'已解决' if consecutive_ok_after else '仍存在'}")
    print(f"最终拐点数: {len(state.confirmed_points)}")
    
    # 显示最终的ZigZag点
    print(f"\n=== 最终ZigZag点 ===")
    for i, point in enumerate(state.confirmed_points):
        print(f"拐点{i+1}: {point.date} {point.point_type} {point.price}")
    
    print("\n测试完成!")
