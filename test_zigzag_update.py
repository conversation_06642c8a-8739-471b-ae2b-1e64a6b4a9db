from dataclasses import dataclass, asdict
from decimal import Decimal, getcontext
from typing import Optional, List, Dict
from datetime import datetime, timedelta

getcontext().prec = 28  # Decimal 精度

@dataclass
class StockData:
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None  # 'HIGH' or 'LOW'
    trend_status: Optional[str] = None       # 'UP' or 'DOWN'

# ----------- ZigZag 点结构（便于持久化与回看） -----------
@dataclass
class ZigZagPoint:
    date: str
    point_type: str      # 'HIGH'/'LOW'/'PENDING_HIGH'/'PENDING_LOW'
    price: Decimal       # 对于 HIGH 用最高价；LOW 用最低价
    source: str = "zz"   # 你可存来源/备注

# ----------- 每个 symbol 的状态（可JSON化） -----------
@dataclass
class ZigZagState:
    symbol: str

    # 参数（每只股票可统一或各自配置）
    atr_period: int = 14
    atr_multiplier: Decimal = Decimal('1.5')
    min_price_move: Decimal = Decimal('0')
    min_trend_bars: int = 1

    # ATR 滚动（Wilder）
    prev_close: Optional[Decimal] = None
    atr: Optional[Decimal] = None
    atr_count: int = 0
    tr_sum: Decimal = Decimal('0')   # 前 period 根TR之和，用于首个ATR

    # 当前趋势
    trend: Optional[str] = None      # 'UP'/'DOWN'/None
    bars_in_trend: int = 0

    # 趋势内极值（随着趋势推进不断更新）
    extreme_high: Optional[Decimal] = None
    extreme_high_date: Optional[str] = None
    extreme_low: Optional[Decimal] = None
    extreme_low_date: Optional[str] = None

    # 已确认拐点 & 末尾PENDING拐点
    confirmed_points: List[ZigZagPoint] = None
    pending_point: Optional[ZigZagPoint] = None

    def __post_init__(self):
        if self.confirmed_points is None:
            self.confirmed_points = []

# ----------- 内部工具：TR/ATR增量更新（Wilder） -----------
def _update_atr(state: ZigZagState, bar: StockData):
    high, low, close = bar.high, bar.low, bar.close
    if state.prev_close is None:
        tr = high - low
    else:
        tr = max(high - low, abs(high - state.prev_close), abs(low - state.prev_close))

    # 初始化阶段：累计 period 根 TR，得到首个 ATR
    if state.atr_count < state.atr_period:
        state.tr_sum += tr
        state.atr_count += 1
        if state.atr_count == state.atr_period:
            state.atr = (state.tr_sum / Decimal(state.atr_period))
    else:
        # Wilder 平滑
        state.atr = ((state.atr * Decimal(state.atr_period - 1)) + tr) / Decimal(state.atr_period)

    state.prev_close = close

# ----------- 内部工具：确认拐点（只在满足反转时） -----------
def _confirm_point(state: ZigZagState, point_type: str, date: str, price: Decimal, stock_data_list: List[StockData] = None):
    # 避免连续同类型（正常不会发生，但做保护：若同类型，则保留更极端）
    if state.confirmed_points and state.confirmed_points[-1].point_type == point_type:
        last = state.confirmed_points[-1]
        if point_type == "HIGH":
            if price > last.price:
                state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
        else:
            if price < last.price:
                state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
    else:
        state.confirmed_points.append(ZigZagPoint(date, point_type, price))

    # 更新对应的 StockData 对象
    if stock_data_list:
        for stock_data in stock_data_list:
            # 将 datetime 对象转换为字符串进行比较
            stock_date_str = stock_data.date.strftime('%Y-%m-%d') if hasattr(stock_data.date, 'strftime') else str(stock_data.date)
            if stock_date_str == date and stock_data.symbol == state.symbol:
                stock_data.is_zigzag_point = True
                stock_data.zigzag_point_type = point_type
                # 根据最近的两个确认点设置趋势状态
                if len(state.confirmed_points) >= 2:
                    last_two = state.confirmed_points[-2:]
                    if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
                        stock_data.trend_status = "UP"
                    elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
                        stock_data.trend_status = "DOWN"
                break

    state.pending_point = None  # 确认后清掉 pending

# ----------- 增量更新主函数：喂一根bar推进状态 -----------
def update_state(state: ZigZagState, bar: StockData, stock_data_list: List[StockData] = None) -> ZigZagState:
    assert state.symbol == bar.symbol, "状态与bar的symbol不一致"

    # 1) 更新 ATR
    _update_atr(state, bar)

    # 2) ATR 未就绪，先积累数据、初始化极值与pending
    if state.atr is None:
        # 初始化极值
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        # 暂时给个 pending（不会确认）
        if state.pending_point is None:
            # 用离收盘更近的方向先立个 pending
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
                state.trend = "DOWN"  # 倾向向下
            else:
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
                state.trend = "UP"    # 倾向向上
        state.bars_in_trend += 1
        return state

    # 3) 正常阶段：根据趋势推进极值 & 判断是否反转
    if state.trend is None:
        # 根据当前bar与极值决定初始趋势，并给 pending
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)

        rng = state.extreme_high - state.extreme_low
        if rng >= state.atr * state.atr_multiplier and rng >= state.min_price_move:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.trend = "DOWN"
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
            else:
                state.trend = "UP"
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
            state.bars_in_trend = 1
        else:
            state.bars_in_trend += 1
        return state

    # 已有趋势
    state.bars_in_trend += 1

    if state.trend == "UP":
        # 更新上升趋势内的最高点（pending_high）
        if state.extreme_high is None or bar.high >= state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

        # 检查是否触发反转为 DOWN（满足 ATR 回撤 + 最小波动 + 最小条数）
        if (state.extreme_high - bar.low) >= state.atr * state.atr_multiplier and \
           (state.extreme_high - bar.low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            # 确认 HIGH 拐点（把 pending_high 变成 HIGH）
            _confirm_point(state, "HIGH", state.extreme_high_date, state.extreme_high, stock_data_list)
            # 切换趋势到 DOWN，并以当前bar.low为新的 pending_low 起点
            state.trend = "DOWN"
            state.bars_in_trend = 1
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

    elif state.trend == "DOWN":
        # 更新下降趋势内的最低点（pending_low）
        if state.extreme_low is None or bar.low <= state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

        # 检查是否触发反转为 UP
        if (bar.high - state.extreme_low) >= state.atr * state.atr_multiplier and \
           (bar.high - state.extreme_low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            # 确认 LOW 拐点
            _confirm_point(state, "LOW", state.extreme_low_date, state.extreme_low, stock_data_list)
            # 切换趋势到 UP
            state.trend = "UP"
            state.bars_in_trend = 1
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

    return state

# ----------- 更新所有数据的趋势状态 -----------
def update_trend_status_for_all(stock_data_list: List[StockData], state: ZigZagState):
    """
    根据确认的zigzag点更新所有数据的趋势状态
    """
    if len(state.confirmed_points) < 2:
        return
    
    # 获取最近的两个确认点
    last_two = state.confirmed_points[-2:]
    current_trend = None
    
    if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
        current_trend = "UP"
    elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
        current_trend = "DOWN"
    
    # 找到最后一个确认点的索引
    last_point_date = last_two[1].date
    last_point_idx = -1
    for i, data in enumerate(stock_data_list):
        data_date_str = data.date.strftime('%Y-%m-%d') if hasattr(data.date, 'strftime') else str(data.date)
        if data_date_str == last_point_date:
            last_point_idx = i
            break
    
    # 从最后一个确认点开始更新趋势状态
    if last_point_idx >= 0 and current_trend:
        for i in range(last_point_idx, len(stock_data_list)):
            stock_data_list[i].trend_status = current_trend

# 创建测试数据
def create_test_data():
    """创建模拟的股票数据用于测试"""
    base_date = datetime(2025, 7, 1)
    stock_data_list = []
    
    # 模拟一个上升然后下降的价格序列
    prices = [
        (10.0, 10.5, 9.8, 10.2),   # 第1天
        (10.2, 10.8, 10.0, 10.6),  # 第2天
        (10.6, 11.2, 10.4, 11.0),  # 第3天 - 可能的高点
        (11.0, 11.1, 10.3, 10.5),  # 第4天
        (10.5, 10.7, 9.8, 10.0),   # 第5天
        (10.0, 10.2, 9.5, 9.8),    # 第6天 - 可能的低点
        (9.8, 10.5, 9.7, 10.3),    # 第7天
        (10.3, 10.9, 10.1, 10.7),  # 第8天
        (10.7, 11.5, 10.5, 11.2),  # 第9天 - 可能的高点
        (11.2, 11.3, 10.6, 10.8),  # 第10天
        (10.8, 10.9, 10.2, 10.4),  # 第11天
        (10.4, 10.6, 9.8, 10.0),   # 第12天
        (10.0, 10.1, 9.3, 9.5),    # 第13天 - 可能的低点
        (9.5, 10.2, 9.4, 9.9),     # 第14天
        (9.9, 10.6, 9.8, 10.4),    # 第15天
    ]
    
    for i, (open_price, high_price, low_price, close_price) in enumerate(prices):
        date = base_date + timedelta(days=i)
        stock_data = StockData(
            symbol="TEST001",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list

if __name__ == "__main__":
    print("开始测试...")

    # 创建测试数据
    stock_data_list = create_test_data()
    print(f"创建了 {len(stock_data_list)} 条测试数据")

    # 初始化状态
    state = ZigZagState(symbol="TEST001", atr_multiplier=Decimal('0.5'))  # 降低阈值以便更容易触发

    print("=== 开始ZigZag分析 ===")

    # 逐日更新
    for i, bar in enumerate(stock_data_list):
        print(f"\n处理第{i+1}天...")
        state = update_state(state, bar, stock_data_list)

        print(f"第{i+1}天 {bar.date.strftime('%Y-%m-%d')}: 开={bar.open} 高={bar.high} 低={bar.low} 收={bar.close}")
        print(f"  ATR: {state.atr}")
        print(f"  趋势: {state.trend}, 已走{state.bars_in_trend}根bar")

        if state.confirmed_points:
            print(f"  已确认拐点数: {len(state.confirmed_points)}")
            for p in state.confirmed_points[-2:]:  # 只显示最近2个
                print(f"    {p.date} {p.point_type} {p.price}")

        if state.pending_point:
            print(f"  Pending点: {state.pending_point.date} {state.pending_point.point_type} {state.pending_point.price}")

        if bar.is_zigzag_point:
            print(f"  *** 这是ZigZag点: {bar.zigzag_point_type} ***")

    # 更新所有数据的趋势状态
    print("\n更新所有数据的趋势状态...")
    update_trend_status_for_all(stock_data_list, state)

    print("\n=== 所有ZigZag点信息 ===")
    zigzag_count = 0
    for i, data in enumerate(stock_data_list):
        if data.is_zigzag_point:
            zigzag_count += 1
            price = data.high if data.zigzag_point_type == 'HIGH' else data.low
            print(f"第{i+1}天 {data.date.strftime('%Y-%m-%d')}: {data.zigzag_point_type} 价格={price} 趋势={data.trend_status}")

    print(f"总共找到 {zigzag_count} 个ZigZag点")

    print(f"\n=== 最后几天的趋势状态 ===")
    for i in range(max(0, len(stock_data_list) - 5), len(stock_data_list)):
        data = stock_data_list[i]
        print(f"第{i+1}天 {data.date.strftime('%Y-%m-%d')}: 趋势={data.trend_status}, ZigZag={data.is_zigzag_point}, 类型={data.zigzag_point_type}")

    print("测试完成!")
