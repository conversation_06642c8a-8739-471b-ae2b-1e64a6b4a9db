from dataclasses import dataclass, asdict
from decimal import Decimal, getcontext
from typing import Optional, List, Dict
import json
from datetime import datetime

from stock_backtest.data_loader import get_stock_data, dataframe_to_stock_data
from stock_backtest.data_models import StockData
from stock_visualization import StockVisualizer

getcontext().prec = 28  # Decimal 精度

# StockData 类现在从 stock_backtest.data_models 导入

# ----------- ZigZag 点结构（便于持久化与回看） -----------
@dataclass
class ZigZagPoint:
    date: str
    point_type: str      # 'HIGH'/'LOW'/'PENDING_HIGH'/'PENDING_LOW'
    price: Decimal       # 对于 HIGH 用最高价；LOW 用最低价
    source: str = "zz"   # 你可存来源/备注

# ----------- 每个 symbol 的状态（可JSON化） -----------
@dataclass
class ZigZagState:
    symbol: str

    # 参数（每只股票可统一或各自配置）
    atr_period: int = 14
    atr_multiplier: Decimal = Decimal('1.5')
    min_price_move: Decimal = Decimal('0')
    min_trend_bars: int = 1

    # ATR 滚动（Wilder）
    prev_close: Optional[Decimal] = None
    atr: Optional[Decimal] = None
    atr_count: int = 0
    tr_sum: Decimal = Decimal('0')   # 前 period 根TR之和，用于首个ATR

    # 当前趋势
    trend: Optional[str] = None      # 'UP'/'DOWN'/None
    bars_in_trend: int = 0

    # 趋势内极值（随着趋势推进不断更新）
    extreme_high: Optional[Decimal] = None
    extreme_high_date: Optional[str] = None
    extreme_low: Optional[Decimal] = None
    extreme_low_date: Optional[str] = None

    # 已确认拐点 & 末尾PENDING拐点
    confirmed_points: List[ZigZagPoint] = None
    pending_point: Optional[ZigZagPoint] = None

    def __post_init__(self):
        if self.confirmed_points is None:
            self.confirmed_points = []

    # ---- 序列化辅助 ----
    def to_dict(self) -> Dict:
        d = asdict(self)
        # Decimal -> str
        for k in ["atr", "tr_sum", "atr_multiplier", "min_price_move",
                  "extreme_high", "extreme_low"]:
            if d[k] is not None:
                d[k] = str(d[k])
        # points Decimal -> str
        for p in d["confirmed_points"]:
            p["price"] = str(p["price"])
        if d["pending_point"] is not None:
            d["pending_point"]["price"] = str(d["pending_point"]["price"])
        return d

    @staticmethod
    def from_dict(d: Dict) -> "ZigZagState":
        # 反序列化 Decimal
        def D(x): return None if x is None else Decimal(x)
        s = ZigZagState(
            symbol=d["symbol"],
            atr_period=d.get("atr_period", 14),
            atr_multiplier=D(d.get("atr_multiplier", "1.5")),
            min_price_move=D(d.get("min_price_move", "0")),
            min_trend_bars=d.get("min_trend_bars", 1),
            prev_close=D(d.get("prev_close")),
            atr=D(d.get("atr")),
            atr_count=d.get("atr_count", 0),
            tr_sum=D(d.get("tr_sum", "0")),
            trend=d.get("trend"),
            bars_in_trend=d.get("bars_in_trend", 0),
            extreme_high=D(d.get("extreme_high")) if d.get("extreme_high") is not None else None,
            extreme_high_date=d.get("extreme_high_date"),
            extreme_low=D(d.get("extreme_low")) if d.get("extreme_low") is not None else None,
            extreme_low_date=d.get("extreme_low_date"),
            confirmed_points=[],
            pending_point=None
        )
        for p in d.get("confirmed_points", []):
            s.confirmed_points.append(
                ZigZagPoint(p["date"], p["point_type"], Decimal(p["price"]), p.get("source", "zz"))
            )
        if d.get("pending_point") is not None:
            pp = d["pending_point"]
            s.pending_point = ZigZagPoint(pp["date"], pp["point_type"], Decimal(pp["price"]), pp.get("source", "zz"))
        return s


# ----------- 内部工具：TR/ATR增量更新（Wilder） -----------
def _update_atr(state: ZigZagState, bar: StockData):
    high, low, close = bar.high, bar.low, bar.close
    if state.prev_close is None:
        tr = high - low
    else:
        tr = max(high - low, abs(high - state.prev_close), abs(low - state.prev_close))

    # 初始化阶段：累计 period 根 TR，得到首个 ATR
    if state.atr_count < state.atr_period:
        state.tr_sum += tr
        state.atr_count += 1
        if state.atr_count == state.atr_period:
            state.atr = (state.tr_sum / Decimal(state.atr_period))
    else:
        # Wilder 平滑
        state.atr = ((state.atr * Decimal(state.atr_period - 1)) + tr) / Decimal(state.atr_period)

    state.prev_close = close
    


# ----------- 内部工具：确认拐点（只在满足反转时） -----------
def _confirm_point(state: ZigZagState, point_type: str, date: str, price: Decimal, stock_data_list: List[StockData] = None):
    # 避免连续同类型（正常不会发生，但做保护：若同类型，则保留更极端）
    if state.confirmed_points and state.confirmed_points[-1].point_type == point_type:
        last = state.confirmed_points[-1]
        if point_type == "HIGH":
            if price > last.price:
                state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
        else:
            if price < last.price:
                state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
    else:
        state.confirmed_points.append(ZigZagPoint(date, point_type, price))

    # 更新对应的 StockData 对象
    if stock_data_list:
        for stock_data in stock_data_list:
            # 将 datetime 对象转换为字符串进行比较
            stock_date_str = stock_data.date.strftime('%Y-%m-%d') if hasattr(stock_data.date, 'strftime') else str(stock_data.date)
            if stock_date_str == date and stock_data.symbol == state.symbol:
                stock_data.is_zigzag_point = True
                stock_data.zigzag_point_type = point_type
                # 根据最近的两个确认点设置趋势状态
                if len(state.confirmed_points) >= 2:
                    last_two = state.confirmed_points[-2:]
                    if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
                        stock_data.trend_status = "UP"
                    elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
                        stock_data.trend_status = "DOWN"
                break

    state.pending_point = None  # 确认后清掉 pending


# ----------- 增量更新主函数：喂一根bar推进状态 -----------
def update_state(state: ZigZagState, bar: StockData, stock_data_list: List[StockData] = None) -> ZigZagState:
    assert state.symbol == bar.symbol, "状态与bar的symbol不一致"

    # 1) 更新 ATR
    _update_atr(state, bar)

    # 2) ATR 未就绪，先积累数据、初始化极值与pending
    if state.atr is None:
        # 初始化极值
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        # 暂时给个 pending（不会确认）
        if state.pending_point is None:
            # 用离收盘更近的方向先立个 pending
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
                state.trend = "DOWN"  # 倾向向下
            else:
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
                state.trend = "UP"    # 倾向向上
        state.bars_in_trend += 1
        return state

    # 3) 正常阶段：根据趋势推进极值 & 判断是否反转
    if state.trend is None:
        # 根据当前bar与极值决定初始趋势，并给 pending
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)

        rng = state.extreme_high - state.extreme_low
        if rng >= state.atr * state.atr_multiplier and rng >= state.min_price_move:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.trend = "DOWN"
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
            else:
                state.trend = "UP"
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
            state.bars_in_trend = 1
        else:
            state.bars_in_trend += 1
        return state

    # 已有趋势
    state.bars_in_trend += 1

    if state.trend == "UP":
        # 更新上升趋势内的最高点（pending_high）
        if state.extreme_high is None or bar.high >= state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

        # 检查是否触发反转为 DOWN（满足 ATR 回撤 + 最小波动 + 最小条数）
        if (state.extreme_high - bar.low) >= state.atr * state.atr_multiplier and \
           (state.extreme_high - bar.low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            # 确认 HIGH 拐点（把 pending_high 变成 HIGH）
            _confirm_point(state, "HIGH", state.extreme_high_date, state.extreme_high, stock_data_list)
            # 切换趋势到 DOWN，并以当前bar.low为新的 pending_low 起点
            state.trend = "DOWN"
            state.bars_in_trend = 1
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

    elif state.trend == "DOWN":
        # 更新下降趋势内的最低点（pending_low）
        if state.extreme_low is None or bar.low <= state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

        # 检查是否触发反转为 UP
        if (bar.high - state.extreme_low) >= state.atr * state.atr_multiplier and \
           (bar.high - state.extreme_low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            # 确认 LOW 拐点
            _confirm_point(state, "LOW", state.extreme_low_date, state.extreme_low, stock_data_list)
            # 切换趋势到 UP
            state.trend = "UP"
            state.bars_in_trend = 1
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

    return state


# ----------- 更新所有数据的趋势状态 -----------
def update_trend_status_for_all(stock_data_list: List[StockData], state: ZigZagState):
    """
    根据确认的zigzag点更新所有数据的趋势状态
    """
    if len(state.confirmed_points) < 2:
        return

    # 获取最近的两个确认点
    last_two = state.confirmed_points[-2:]
    current_trend = None

    if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
        current_trend = "UP"
    elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
        current_trend = "DOWN"

    # 找到最后一个确认点的索引
    last_point_date = last_two[1].date
    last_point_idx = -1
    for i, data in enumerate(stock_data_list):
        data_date_str = data.date.strftime('%Y-%m-%d') if hasattr(data.date, 'strftime') else str(data.date)
        if data_date_str == last_point_date:
            last_point_idx = i
            break

    # 从最后一个确认点开始更新趋势状态
    if last_point_idx >= 0 and current_trend:
        for i in range(last_point_idx, len(stock_data_list)):
            stock_data_list[i].trend_status = current_trend


# ----------- 批量更新（股票池） -----------
def update_pool(states: Dict[str, ZigZagState], today_bars: List[StockData]) -> Dict[str, ZigZagState]:
    """
    states: {symbol -> ZigZagState}（从DB取出来反序列化即可）
    today_bars: 多只股票同日期的bar列表
    返回：更新后的 states（也可就地更新，然后持久化）
    """
    for bar in today_bars:
        if bar.symbol not in states:
            states[bar.symbol] = ZigZagState(symbol=bar.symbol)  # 用默认参数初始化
        states[bar.symbol] = update_state(states[bar.symbol], bar)
    return states



stock_code = '002324'
start_date ='20090701'
end_date = '20250820'
df = get_stock_data(stock_code, start_date, end_date)
stock_data_list = dataframe_to_stock_data(df, stock_code)

# ---- 股票池状态，通常从数据库加载；第一次运行时为空 ----
states = {}

# 模拟逐日更新
for i in range(len(stock_data_list)):
    symbol = stock_data_list[i].symbol
    if symbol not in states:
        states[symbol] = ZigZagState(symbol=symbol) 
    st = update_state(states[symbol], stock_data_list[i], stock_data_list)



    print(f"股票 {symbol} 状态：")
    print("  已确认拐点：")
    for p in st.confirmed_points:
        print(f"    {p.date} {p.point_type} {p.price}")
    if st.pending_point:
        print(f"  末尾pending点：{st.pending_point.date} {st.pending_point.point_type} {st.pending_point.price}")
    print("  当前趋势：", st.trend)
    print("  已走的bar数：", st.bars_in_trend)

    # 显示当前bar的zigzag信息
    current_bar = stock_data_list[i]
    if current_bar.is_zigzag_point:
        print(f"  当前bar是zigzag点：{current_bar.date} {current_bar.zigzag_point_type} 趋势:{current_bar.trend_status}")
    print()

# 更新所有数据的趋势状态
for symbol, state in states.items():
    update_trend_status_for_all(stock_data_list, state)

print("验证结果...")

def validate_zigzag_points(data: List[StockData], window: int = 5):
    """
    验证ZigZag点是否确实是局部极值
    """
    print("\n=== 验证ZigZag点 ===")
    for i, point in enumerate(data):
        if point.is_zigzag_point:
            # 检查窗口范围内是否确实是极值
            start_idx = max(0, i - window)
            end_idx = min(len(data), i + window + 1)
            
            if point.zigzag_point_type == 'HIGH':
                window_highs = [data[j].high for j in range(start_idx, end_idx)]
                is_valid = point.high == max(window_highs)
                print(f"索引{i} HIGH点 {point.high}: {'✓' if is_valid else '✗'} "
                      f"(窗口{start_idx}-{end_idx-1}最高{max(window_highs)})")
                
            elif point.zigzag_point_type == 'LOW':
                window_lows = [data[j].low for j in range(start_idx, end_idx)]
                is_valid = point.low == min(window_lows)
                print(f"索引{i} LOW点 {point.low}: {'✓' if is_valid else '✗'} "
                      f"(窗口{start_idx}-{end_idx-1}最低{min(window_lows)})")

validate_zigzag_points(stock_data_list)

print("正在生成可视化图表...")
    
# 创建可视化
visualizer = StockVisualizer()
output_file = f"{stock_code}_analysis_{start_date}_{end_date}.html"
filename = visualizer.save_chart(stock_data_list, output_file, stock_code)

# # ---- 序列化到JSON（可存数据库/文件）----
# states_json = {sym: st.to_dict() for sym, st in states.items()}
# print("持久化JSON：")
# print(json.dumps(states_json, ensure_ascii=False, indent=2, default=str))

# # ---- 反序列化（从数据库/文件恢复）----
# recovered_states = {sym: ZigZagState.from_dict(d) for sym, d in states_json.items()}
