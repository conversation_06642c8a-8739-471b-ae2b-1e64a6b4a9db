import aio_pika
import asyncio
import json
import logging
import datetime
from decimal import Decimal


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def common_json_encoder(obj):
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    if isinstance(obj, set):
        return list(obj)
    if isinstance(obj, Decimal):
        return str(obj)
    # 你可以在这里添加更多自定义类型的处理
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

class AsyncRabbitMQClient:
    """
    一个通用的异步 RabbitMQ 客户端类，用于消息的发布和消费。
    使用 aio_pika 库。
    """

    def __init__(self, host='localhost', port=5672, login='guest', password='guest', virtualhost='/'):
        """
        初始化 AsyncRabbitMQClient。

        Args:
            host (str): RabbitMQ 服务器地址。
            port (int): RabbitMQ 服务器端口。
            login (str): 连接 RabbitMQ 的用户名。
            password (str): 连接 RabbitMQ 的密码。
            virtualhost (str): 虚拟主机。
        """
        self.host = host
        self.port = port
        self.login = login
        self.password = password
        self.virtualhost = virtualhost
        self.connection = None
        self.channel = None
        self.is_connected = False # 保持这个状态变量

    async def connect(self):
        """
        异步建立到 RabbitMQ 的连接并创建通道。
        如果连接失败，会进行重试。
        """
        while not self.is_connected:
            try:
                logging.info(f"尝试连接 RabbitMQ: {self.host}:{self.port}")
                self.connection = await aio_pika.connect_robust(
                    host=self.host,
                    port=self.port,
                    login=self.login,
                    password=self.password,
                    virtualhost=self.virtualhost,
                    loop=asyncio.get_running_loop()
                )
                self.channel = await self.connection.channel()
                self.is_connected = True
                logging.info("成功连接到 RabbitMQ。")

                # *** 移除 self.connection.add_close_callback(self._on_connection_closed) ***
                # connect_robust 会自动处理重连，无需手动添加 close callback

            except aio_pika.exceptions.AMQPConnectionError as e:
                logging.error(f"连接 RabbitMQ 失败: {e}. 5秒后重试...")
                await asyncio.sleep(5)
            except Exception as e:
                logging.error(f"连接 RabbitMQ 时发生未知错误: {e}. 5秒后重试...")
                await asyncio.sleep(5)

    # *** _on_connection_closed 方法可以移除，因为不再被 add_close_callback 调用 ***
    # 如果你需要监听连接状态，请查阅 aio_pika 的信号机制 (e.g., connection.connected, connection.disconnected)

    async def declare_queue(self, queue_name: str, durable: bool = True, auto_delete: bool = False):
        """
        异步声明一个队列。

        Args:
            queue_name (str): 队列名称。
            durable (bool): 如果为 True，队列将在 RabbitMQ 重启后依然存在。
            auto_delete (bool): 如果为 True，当最后一个消费者取消订阅时，队列会被删除。
        """
        if not self.channel or self.channel.is_closed:
            logging.warning("通道未就绪或已关闭，尝试重新连接。")
            await self.connect() # 确保通道可用

        try:
            queue = await self.channel.declare_queue(
                name=queue_name,
                durable=durable,
                auto_delete=auto_delete
            )
            logging.info(f"队列 '{queue_name}' 声明成功。")
            return queue
        except Exception as e:
            logging.error(f"声明队列 '{queue_name}' 失败: {e}")
            raise

    async def publish_message(self, queue_name: str, message: dict | str, delivery_mode: aio_pika.DeliveryMode = aio_pika.DeliveryMode.PERSISTENT, exchange_name: str = ''):
        """
        异步发布一条消息到指定的队列。

        Args:
            queue_name (str): 目标队列名称。
            message (dict or str): 要发布的消息内容。如果是字典，会转换为 JSON 字符串。
            delivery_mode (aio_pika.DeliveryMode): 消息的投递模式，默认为持久化。
            exchange_name (str): 交换机名称，默认为空字符串（默认交换机）。
        """
        if not self.channel or self.channel.is_closed:
            logging.warning("通道未就绪或已关闭，尝试重新连接。")
            await self.connect() # 确保通道可用

        if isinstance(message, dict):
            message_body = json.dumps(message, default=common_json_encoder).encode('utf-8')
            content_type = 'application/json'
        else:
            message_body = str(message).encode('utf-8')
            content_type = 'text/plain'

        try:
            message_obj = aio_pika.Message(
                body=message_body,
                content_type=content_type,
                delivery_mode=delivery_mode
            )
            exchange = await self.channel.get_exchange(exchange_name) if exchange_name else self.channel.default_exchange

            await exchange.publish(
                message_obj,
                routing_key=queue_name
            )
            logging.info(f"消息已发布到队列 '{queue_name}': {message}")
        except Exception as e:
            logging.error(f"发布消息到队列 '{queue_name}' 失败: {e}")
            raise

    async def consume_messages(self, queue_name: str, callback_function, prefetch_count: int = 1):
        """
        从指定的队列异步消费消息。

        Args:
            queue_name (str): 要消费的队列名称。
            callback_function (callable): 处理接收到的消息的异步回调函数。
                                          函数签名应为: async def callback(message: aio_pika.IncomingMessage)
            prefetch_count (int): 每次从队列预取的消息数量。控制消费者并发处理能力。
        """
        if not self.channel or self.channel.is_closed:
            logging.warning("通道未就绪或已关闭，尝试重新连接。")
            await self.connect() # 确保通道可用

        try:
            await self.channel.set_qos(prefetch_count=prefetch_count)
            queue = await self.declare_queue(queue_name, durable=True)

            logging.info(f"开始从队列 '{queue_name}' 异步消费消息。")
            async with queue.iterator() as queue_iter:
                async for message in queue_iter:
                    async with message.process():
                        try:
                            await callback_function(message)
                        except Exception as e:
                            logging.error(f"处理消息失败: {e}. 消息内容: {message.body.decode()}. 消息将重新入队。")
                            await message.reject(requeue=True)
        except asyncio.CancelledError:
            logging.info("消费任务被取消。")
        except Exception as e:
            logging.error(f"消费消息时发生未知错误: {e}")
            # 在这里，因为 connect_robust 会自动重连，如果只是连接临时断开，它会尝试恢复。
            # 但如果通道彻底关闭且无法恢复，这里的异常可能会导致 consume_messages 退出。
            # 对于生产系统，你可能需要一个外部的监控或任务管理器来重新启动消费。
        finally:
            # 注意：如果 consume_messages 正常退出（例如任务被取消），这里会关闭连接。
            # 如果是 connect_robust 内部的重连，它不会调用这里的 close。
            await self.close()

    async def close(self):
        """
        异步关闭 RabbitMQ 连接。
        """
        if self.connection and not self.connection.is_closed:
            await self.connection.close()
            self.is_connected = False # 更新连接状态
            logging.info("RabbitMQ 连接已关闭。")


## 如何使用 `aio_pika` 客户端
### 1. 异步发布者 (Publisher) 示例
async def main_async_publisher():
    client = AsyncRabbitMQClient(host='***********', login='sysadmin', password='1q2w3e')
    await client.connect() # 建立连接
    queue_name = 'my_async_test_queue'
    await client.declare_queue(queue_name, durable=True) # 声明持久化队列

    for i in range(10):
        message_data = {"id": i+10, "content": f"Hello Async RabbitMQ! Message {i}"}
        await client.publish_message(
            queue_name,
            message_data,
            delivery_mode=aio_pika.DeliveryMode.PERSISTENT # 确保消息持久化
        )
        await asyncio.sleep(0.5) # 模拟异步操作

    await client.close()
    print("发布者完成。")

if __name__ == "__main__":
    asyncio.run(main_async_publisher())