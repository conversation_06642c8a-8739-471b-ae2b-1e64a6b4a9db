from typing import List, Dict, Any, Union
from decimal import Decimal
from datetime import datetime
from .data_models import StockData, TradingSignal
from .indicators import TechnicalIndicators
from .signal_generator import SignalGenerator, ZigZagFibonacciSignalGenerator

class BacktestEngine:
    """回测引擎"""

    def __init__(self, initial_capital=100000, commission_rate=0.001,
                 signal_generator: Union[SignalGenerator, ZigZagFibonacciSignalGenerator] = None,
                 stop_loss_pct=0.05, take_profit_pct=0.15):
        self.initial_capital = Decimal(str(initial_capital))
        self.commission_rate = Decimal(str(commission_rate))
        self.signal_generator = signal_generator or SignalGenerator()
        self.stop_loss_pct = Decimal(str(stop_loss_pct))  # 止损百分比，默认5%
        self.take_profit_pct = Decimal(str(take_profit_pct))  # 止盈百分比，默认15%
        
    def run_backtest(self, stock_data_dict: Dict[str, List[StockData]]) -> Dict[str, Any]:
        """运行回测"""
        results = {}

        for symbol, data in stock_data_dict.items():
            # 计算技术指标（包括布林线）用于可视化
            # ZigZag策略虽然不使用技术指标生成信号，但需要布林线用于可视化
            processed_data = self._calculate_indicators(data)

            # 生成交易信号
            signals = self.signal_generator.generate_signals(processed_data)

            # 执行回测
            backtest_result = self._execute_backtest(processed_data, signals)

            # 为ZigZag策略添加额外信息
            result_data = {
                'data': processed_data,
                'signals': signals,
                'performance': backtest_result
            }

            # 如果是ZigZag策略，添加ZigZag点信息
            if isinstance(self.signal_generator, ZigZagFibonacciSignalGenerator):
                zigzag_points = self.signal_generator._calculate_zigzag(processed_data)
                result_data['zigzag_points'] = zigzag_points

            results[symbol] = result_data

        return results
    
    def _calculate_indicators(self, data: List[StockData]) -> List[StockData]:
        """计算技术指标"""
        prices = [item.close for item in data]
        
        # 计算MACD
        macd, signal, histogram = TechnicalIndicators.calculate_macd(prices)
        
        # 计算布林线（9天平均线，1倍标准差）
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.calculate_bollinger_bands(prices, period=9, std_dev=1)
        
        # 更新数据
        enhanced_data = []
        for i, item in enumerate(data):
            new_item = StockData(
                symbol=item.symbol,
                date=item.date,
                open=item.open,
                high=item.high,
                low=item.low,
                close=item.close,
                volume=item.volume,
                macd=macd[i],
                macd_signal=signal[i],
                macd_histogram=histogram[i],
                bb_upper=bb_upper[i],
                bb_middle=bb_middle[i],
                bb_lower=bb_lower[i]
            )
            enhanced_data.append(new_item)
        
        return enhanced_data
    
    def _execute_backtest(self, data: List[StockData], signals: List[TradingSignal]) -> Dict[str, Any]:
        """执行回测逻辑，包含止损和止盈功能"""
        capital = self.initial_capital
        position = 0  # 持仓数量
        trades = []
        buy_price = None  # 记录买入价格，用于止损止盈计算

        signal_dict = {signal.date: signal for signal in signals}

        for stock_data in data:
            # 检查止损和止盈条件（如果有持仓）
            if position > 0 and buy_price is not None:
                # 计算当前价格相对于买入价格的变化
                price_decline = (buy_price - stock_data.low) / buy_price
                price_gain = (stock_data.high - buy_price) / buy_price

                # 检查止盈条件（优先级高于止损）
                if price_gain >= self.take_profit_pct:
                    # 触发止盈，按当日最高价卖出
                    take_profit_price = stock_data.high
                    proceeds = position * take_profit_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'TAKE_PROFIT',
                        'shares': position,
                        'price': take_profit_price,
                        'proceeds': proceeds,
                        'reason': f'止盈卖出 (涨幅: {price_gain:.2%})'
                    })
                    position = 0
                    buy_price = None
                    continue  # 止盈后不再处理当日的其他信号

                # 检查止损条件
                elif price_decline >= self.stop_loss_pct:
                    # 触发止损，按当日最低价卖出
                    stop_loss_price = stock_data.low
                    proceeds = position * stop_loss_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'STOP_LOSS',
                        'shares': position,
                        'price': stop_loss_price,
                        'proceeds': proceeds,
                        'reason': f'止损卖出 (跌幅: {price_decline:.2%})'
                    })
                    position = 0
                    buy_price = None
                    continue  # 止损后不再处理当日的其他信号

            # 处理交易信号
            if stock_data.date in signal_dict:
                signal = signal_dict[stock_data.date]

                if signal.signal_type == 'BUY' and capital > 0 and position == 0:
                    # 买入 - 只有在没有持仓时才买入
                    max_shares = int(capital / (signal.price * (Decimal(1) + self.commission_rate)))
                    if max_shares > 0:
                        cost = max_shares * signal.price * (Decimal(1) + self.commission_rate)
                        capital -= cost
                        position += max_shares
                        buy_price = signal.price  # 记录买入价格
                        trades.append({
                            'date': signal.date,
                            'type': 'BUY',
                            'shares': max_shares,
                            'price': signal.price,
                            'cost': cost,
                            'reason': signal.reason
                        })

                elif signal.signal_type == 'SELL' and position > 0:
                    # 卖出
                    proceeds = position * signal.price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': signal.date,
                        'type': 'SELL',
                        'shares': position,
                        'price': signal.price,
                        'proceeds': proceeds,
                        'reason': signal.reason
                    })
                    position = 0
                    buy_price = None  # 清除买入价格记录

        # 计算最终价值
        final_price = data[-1].close if data else Decimal(0)
        final_value = capital + position * final_price

        # 统计止损和止盈次数
        stop_loss_trades = [t for t in trades if t['type'] == 'STOP_LOSS']
        take_profit_trades = [t for t in trades if t['type'] == 'TAKE_PROFIT']

        return {
            'initial_capital': self.initial_capital,
            'final_value': final_value,
            'total_return': (final_value - self.initial_capital) / self.initial_capital,
            'trades': trades,
            'final_position': position,
            'stop_loss_count': len(stop_loss_trades),
            'stop_loss_pct': float(self.stop_loss_pct),
            'take_profit_count': len(take_profit_trades),
            'take_profit_pct': float(self.take_profit_pct)
        }
