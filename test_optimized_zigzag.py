#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataclasses import dataclass, asdict
from decimal import Decimal, getcontext
from typing import Optional, List, Dict
from datetime import datetime, timedelta

# 导入我们的优化后的函数
from main2 import ZigZagState, ZigZagPoint, update_state, _confirm_point, _update_trend_status_from_point, _clear_zigzag_point_marking
from stock_backtest.data_models import StockData

getcontext().prec = 28

def create_test_data_with_consecutive_peaks():
    """
    创建包含连续高点和低点的测试数据，用于验证优化效果
    """
    base_date = datetime(2025, 7, 1)
    stock_data_list = []
    
    # 模拟包含连续高点的价格序列
    prices = [
        (10.0, 10.5, 9.8, 10.2),   # 第1天
        (10.2, 10.8, 10.0, 10.6),  # 第2天
        (10.6, 11.2, 10.4, 11.0),  # 第3天 - 第一个高点
        (11.0, 11.5, 10.8, 11.3),  # 第4天 - 更高的高点（应该替换第3天）
        (11.3, 11.1, 10.5, 10.7),  # 第5天
        (10.7, 10.8, 9.8, 10.0),   # 第6天
        (10.0, 10.2, 9.3, 9.5),    # 第7天 - 第一个低点
        (9.5, 9.8, 9.0, 9.2),      # 第8天 - 更低的低点（应该替换第7天）
        (9.2, 10.0, 9.1, 9.8),     # 第9天
        (9.8, 10.5, 9.7, 10.3),    # 第10天
        (10.3, 11.0, 10.1, 10.8),  # 第11天
        (10.8, 11.8, 10.6, 11.5),  # 第12天 - 新的高点
        (11.5, 11.6, 11.0, 11.2),  # 第13天
        (11.2, 11.3, 10.5, 10.8),  # 第14天
        (10.8, 10.9, 10.0, 10.2),  # 第15天
    ]
    
    for i, (open_price, high_price, low_price, close_price) in enumerate(prices):
        date = base_date + timedelta(days=i)
        stock_data = StockData(
            symbol="TEST001",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list

def test_consecutive_points_handling():
    """
    测试连续高点/低点的处理
    """
    print("=== 测试连续高点/低点处理 ===")
    
    stock_data_list = create_test_data_with_consecutive_peaks()
    state = ZigZagState(symbol="TEST001", atr_multiplier=Decimal('0.3'))  # 降低阈值
    
    print("处理数据...")
    for i, bar in enumerate(stock_data_list):
        state = update_state(state, bar, stock_data_list)
        
        if bar.is_zigzag_point:
            print(f"第{i+1}天 {bar.date.strftime('%Y-%m-%d')}: {bar.zigzag_point_type} 点 "
                  f"价格={bar.high if bar.zigzag_point_type == 'HIGH' else bar.low} "
                  f"趋势={bar.trend_status}")
    
    print(f"\n总确认拐点数: {len(state.confirmed_points)}")
    for i, point in enumerate(state.confirmed_points):
        print(f"拐点{i+1}: {point.date} {point.point_type} {point.price}")
    
    # 验证没有连续的同类型点
    consecutive_count = 0
    for i in range(1, len(state.confirmed_points)):
        if state.confirmed_points[i].point_type == state.confirmed_points[i-1].point_type:
            consecutive_count += 1
            print(f"⚠️  发现连续同类型点: {state.confirmed_points[i-1].point_type} -> {state.confirmed_points[i].point_type}")
    
    if consecutive_count == 0:
        print("✅ 没有连续的同类型点，优化成功！")
    else:
        print(f"❌ 发现 {consecutive_count} 个连续同类型点")
    
    return stock_data_list, state

def test_trend_status_efficiency():
    """
    测试趋势状态更新的效率
    """
    print("\n=== 测试趋势状态更新效率 ===")
    
    stock_data_list, state = test_consecutive_points_handling()
    
    # 检查趋势状态是否正确设置
    trend_changes = []
    current_trend = None
    
    for i, data in enumerate(stock_data_list):
        if data.trend_status != current_trend:
            trend_changes.append((i+1, data.date.strftime('%Y-%m-%d'), data.trend_status))
            current_trend = data.trend_status
    
    print("趋势变化:")
    for day, date, trend in trend_changes:
        print(f"第{day}天 {date}: 趋势变为 {trend}")
    
    # 验证ZigZag点附近的趋势状态
    print("\nZigZag点附近的趋势状态:")
    for i, data in enumerate(stock_data_list):
        if data.is_zigzag_point:
            print(f"第{i+1}天 {data.date.strftime('%Y-%m-%d')}: {data.zigzag_point_type} 点, 趋势={data.trend_status}")
            # 检查后续几天的趋势状态
            for j in range(i+1, min(i+4, len(stock_data_list))):
                next_data = stock_data_list[j]
                print(f"  第{j+1}天: 趋势={next_data.trend_status}")

def validate_local_extremes(stock_data_list: List[StockData], window: int = 3):
    """
    验证ZigZag点是否确实是局部极值
    """
    print(f"\n=== 验证局部极值（窗口大小={window}） ===")
    
    valid_count = 0
    invalid_count = 0
    
    for i, data in enumerate(stock_data_list):
        if data.is_zigzag_point:
            start_idx = max(0, i - window)
            end_idx = min(len(stock_data_list), i + window + 1)
            
            is_valid = False
            if data.zigzag_point_type == 'HIGH':
                window_highs = [stock_data_list[j].high for j in range(start_idx, end_idx)]
                is_valid = data.high == max(window_highs)
                print(f"第{i+1}天 HIGH点 {data.high}: {'✅' if is_valid else '❌'} "
                      f"(窗口{start_idx+1}-{end_idx}最高{max(window_highs)})")
            else:  # LOW
                window_lows = [stock_data_list[j].low for j in range(start_idx, end_idx)]
                is_valid = data.low == min(window_lows)
                print(f"第{i+1}天 LOW点 {data.low}: {'✅' if is_valid else '❌'} "
                      f"(窗口{start_idx+1}-{end_idx}最低{min(window_lows)})")
            
            if is_valid:
                valid_count += 1
            else:
                invalid_count += 1
    
    total = valid_count + invalid_count
    if total > 0:
        print(f"\n局部极值验证结果: {valid_count}/{total} ({valid_count/total*100:.1f}%) 有效")
    else:
        print("\n没有找到ZigZag点")

if __name__ == "__main__":
    print("开始测试优化后的ZigZag算法...")
    
    # 测试1: 连续高点/低点处理
    stock_data_list, state = test_consecutive_points_handling()
    
    # 测试2: 趋势状态更新效率
    test_trend_status_efficiency()
    
    # 测试3: 验证局部极值
    validate_local_extremes(stock_data_list)
    
    print("\n测试完成!")
