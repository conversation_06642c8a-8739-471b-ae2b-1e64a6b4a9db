<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="29a548561044483f913b280312f94cd1" class="chart-container" style="width:100%; height:800px; "></div>
    <script>
        var chart_29a548561044483f913b280312f94cd1 = echarts.init(
            document.getElementById('29a548561044483f913b280312f94cd1'), 'white', {renderer: 'canvas'});
            window.stockDataForTooltip = [{"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 917026}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1085929}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 914629}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1175400}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1108944}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 883707}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 976472}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 980331}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1081137}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 953709}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1102698}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 951722}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 999295}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 994081}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1158375}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1182272}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1135544}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 829327}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 940373}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1176393}, {"trend_status": "暂无", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 1137039}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1094318}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1105939}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1067138}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 880132}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 833306}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 931813}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1177865}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1136048}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 801701}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1062451}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1127837}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 880131}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1078058}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 858650}, {"trend_status": "暂无", "is_zigzag_point": true, "zigzag_point_type": "HIGH", "volume": 961225}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 841291}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1198774}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1088255}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 911043}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1173790}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1029690}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 833568}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 915457}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 835337}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 924782}, {"trend_status": "暂无", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 1179245}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1047974}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1025994}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 828401}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1011089}, {"trend_status": "暂无", "is_zigzag_point": true, "zigzag_point_type": "HIGH", "volume": 900448}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 946037}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1088528}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 807737}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 887194}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 830741}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1038555}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1091382}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 914137}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1192154}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1078463}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 897425}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1011694}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1111699}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1096341}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1175490}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1138428}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 838033}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 852418}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 982981}, {"trend_status": "暂无", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 882704}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1142871}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1147806}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 856117}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 910430}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 938403}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 826633}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 801858}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 884715}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 805069}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 878147}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 877643}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 820916}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 853893}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1125405}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 885197}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 812995}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1015856}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 939883}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 820302}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1041331}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 916876}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 836396}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1009546}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 814470}, {"trend_status": "UP", "is_zigzag_point": true, "zigzag_point_type": "HIGH", "volume": 939180}, {"trend_status": "UP", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 964457}, {"trend_status": "UP", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1102298}, {"trend_status": "UP", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 1072587}]
            window.zigzagValidation = {"total_points": 6, "zigzag_points": [{"index": 20, "date": "2025-07-21", "type": "LOW", "price": 30.99, "close": 31.18}, {"index": 35, "date": "2025-08-05", "type": "HIGH", "price": 34.84, "close": 34.39}, {"index": 46, "date": "2025-08-16", "type": "LOW", "price": 30.34, "close": 30.58}, {"index": 51, "date": "2025-08-21", "type": "HIGH", "price": 31.88, "close": 31.5}, {"index": 71, "date": "2025-09-10", "type": "LOW", "price": 22.96, "close": 23.73}, {"index": 96, "date": "2025-10-05", "type": "HIGH", "price": 27.51, "close": 26.62}], "consecutive_issues": [], "is_valid": true}
            document.addEventListener('DOMContentLoaded', function() {  });
        var option_29a548561044483f913b280312f94cd1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    30.0,
                    30.49,
                    29.88,
                    30.5
                ],
                [
                    30.49,
                    30.08,
                    29.74,
                    30.54
                ],
                [
                    30.08,
                    29.57,
                    29.56,
                    30.27
                ],
                [
                    29.57,
                    29.33,
                    29.09,
                    29.84
                ],
                [
                    29.33,
                    29.83,
                    29.24,
                    30.08
                ],
                [
                    29.83,
                    29.67,
                    29.34,
                    30.22
                ],
                [
                    29.67,
                    30.26,
                    29.6,
                    30.42
                ],
                [
                    30.26,
                    29.78,
                    29.62,
                    30.44
                ],
                [
                    29.78,
                    30.21,
                    29.46,
                    30.57
                ],
                [
                    30.21,
                    29.77,
                    29.73,
                    30.62
                ],
                [
                    29.77,
                    30.59,
                    29.38,
                    30.88
                ],
                [
                    30.59,
                    30.27,
                    29.97,
                    30.63
                ],
                [
                    30.27,
                    31.4,
                    29.88,
                    31.8
                ],
                [
                    31.4,
                    31.23,
                    31.06,
                    31.7
                ],
                [
                    31.23,
                    31.21,
                    30.88,
                    31.55
                ],
                [
                    31.21,
                    31.74,
                    31.13,
                    32.03
                ],
                [
                    31.74,
                    31.5,
                    31.38,
                    31.96
                ],
                [
                    31.5,
                    32.11,
                    31.35,
                    32.22
                ],
                [
                    32.11,
                    31.85,
                    31.7,
                    32.13
                ],
                [
                    31.85,
                    31.27,
                    31.01,
                    32.28
                ],
                [
                    31.27,
                    31.18,
                    30.99,
                    31.58
                ],
                [
                    31.18,
                    31.35,
                    31.06,
                    31.47
                ],
                [
                    31.35,
                    31.67,
                    31.15,
                    32.03
                ],
                [
                    31.67,
                    31.73,
                    31.2,
                    31.84
                ],
                [
                    31.73,
                    31.98,
                    31.32,
                    32.34
                ],
                [
                    31.98,
                    32.48,
                    31.77,
                    32.86
                ],
                [
                    32.48,
                    32.51,
                    32.25,
                    32.8
                ],
                [
                    32.51,
                    33.69,
                    32.51,
                    34.13
                ],
                [
                    33.69,
                    33.18,
                    32.81,
                    34.14
                ],
                [
                    33.18,
                    33.13,
                    33.05,
                    33.33
                ],
                [
                    33.13,
                    33.83,
                    33.0,
                    34.28
                ],
                [
                    33.83,
                    34.16,
                    33.78,
                    34.42
                ],
                [
                    34.16,
                    33.54,
                    33.24,
                    34.49
                ],
                [
                    33.54,
                    33.09,
                    32.61,
                    33.62
                ],
                [
                    33.09,
                    33.72,
                    32.84,
                    34.02
                ],
                [
                    33.72,
                    34.39,
                    33.3,
                    34.84
                ],
                [
                    34.39,
                    33.64,
                    33.35,
                    34.51
                ],
                [
                    33.64,
                    32.6,
                    32.57,
                    33.89
                ],
                [
                    32.6,
                    32.47,
                    32.15,
                    32.66
                ],
                [
                    32.47,
                    31.62,
                    31.34,
                    32.73
                ],
                [
                    31.62,
                    32.25,
                    31.3,
                    32.61
                ],
                [
                    32.25,
                    31.69,
                    31.38,
                    32.73
                ],
                [
                    31.69,
                    32.26,
                    31.57,
                    32.48
                ],
                [
                    32.26,
                    31.75,
                    31.64,
                    32.54
                ],
                [
                    31.75,
                    30.62,
                    30.6,
                    32.09
                ],
                [
                    30.62,
                    31.18,
                    30.59,
                    31.59
                ],
                [
                    31.18,
                    30.58,
                    30.34,
                    31.41
                ],
                [
                    30.58,
                    31.2,
                    30.37,
                    31.47
                ],
                [
                    31.2,
                    31.59,
                    31.15,
                    31.68
                ],
                [
                    31.59,
                    31.12,
                    30.72,
                    31.78
                ],
                [
                    31.12,
                    31.26,
                    31.08,
                    31.72
                ],
                [
                    31.26,
                    31.5,
                    31.21,
                    31.88
                ],
                [
                    31.5,
                    30.72,
                    30.53,
                    31.71
                ],
                [
                    30.72,
                    30.47,
                    30.44,
                    31.13
                ],
                [
                    30.47,
                    29.55,
                    29.31,
                    30.77
                ],
                [
                    29.55,
                    30.21,
                    29.18,
                    30.63
                ],
                [
                    30.21,
                    29.86,
                    29.47,
                    30.43
                ],
                [
                    29.86,
                    29.08,
                    28.91,
                    29.86
                ],
                [
                    29.08,
                    28.53,
                    28.22,
                    29.38
                ],
                [
                    28.53,
                    28.63,
                    28.45,
                    28.84
                ],
                [
                    28.63,
                    29.27,
                    28.4,
                    29.52
                ],
                [
                    29.27,
                    28.76,
                    28.56,
                    29.29
                ],
                [
                    28.76,
                    28.0,
                    27.97,
                    29.18
                ],
                [
                    28.0,
                    27.11,
                    26.76,
                    28.03
                ],
                [
                    27.11,
                    26.33,
                    26.23,
                    27.47
                ],
                [
                    26.33,
                    25.44,
                    25.19,
                    26.36
                ],
                [
                    25.44,
                    25.32,
                    25.25,
                    25.8
                ],
                [
                    25.32,
                    24.89,
                    24.84,
                    25.42
                ],
                [
                    24.89,
                    24.44,
                    24.17,
                    25.01
                ],
                [
                    24.44,
                    23.58,
                    23.38,
                    24.67
                ],
                [
                    23.58,
                    23.09,
                    23.0,
                    23.65
                ],
                [
                    23.09,
                    23.73,
                    22.96,
                    24.05
                ],
                [
                    23.73,
                    23.76,
                    23.63,
                    23.96
                ],
                [
                    23.76,
                    23.92,
                    23.57,
                    24.16
                ],
                [
                    23.92,
                    23.47,
                    23.38,
                    24.24
                ],
                [
                    23.47,
                    23.93,
                    23.37,
                    23.99
                ],
                [
                    23.93,
                    24.36,
                    23.71,
                    24.44
                ],
                [
                    24.36,
                    24.49,
                    24.03,
                    24.59
                ],
                [
                    24.49,
                    24.02,
                    23.92,
                    24.65
                ],
                [
                    24.02,
                    23.9,
                    23.55,
                    24.07
                ],
                [
                    23.9,
                    24.36,
                    23.74,
                    24.57
                ],
                [
                    24.36,
                    23.92,
                    23.67,
                    24.71
                ],
                [
                    23.92,
                    24.1,
                    23.71,
                    24.4
                ],
                [
                    24.1,
                    24.12,
                    23.97,
                    24.14
                ],
                [
                    24.12,
                    24.82,
                    24.03,
                    24.9
                ],
                [
                    24.82,
                    24.73,
                    24.4,
                    25.03
                ],
                [
                    24.73,
                    25.22,
                    24.64,
                    25.57
                ],
                [
                    25.22,
                    26.07,
                    24.89,
                    26.38
                ],
                [
                    26.07,
                    25.7,
                    25.39,
                    26.43
                ],
                [
                    25.7,
                    26.29,
                    25.38,
                    26.63
                ],
                [
                    26.29,
                    25.88,
                    25.74,
                    26.57
                ],
                [
                    25.88,
                    26.57,
                    25.57,
                    26.66
                ],
                [
                    26.57,
                    26.46,
                    26.12,
                    26.9
                ],
                [
                    26.46,
                    25.84,
                    25.71,
                    26.54
                ],
                [
                    25.84,
                    26.69,
                    25.59,
                    26.8
                ],
                [
                    26.69,
                    27.11,
                    26.56,
                    27.46
                ],
                [
                    27.11,
                    26.62,
                    26.55,
                    27.51
                ],
                [
                    26.62,
                    26.02,
                    25.88,
                    26.86
                ],
                [
                    26.02,
                    26.05,
                    25.97,
                    26.43
                ],
                [
                    26.05,
                    25.7,
                    25.53,
                    26.07
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#ec0000",
                "borderColor0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "ZigZag\u8d8b\u52bf\u7ebf",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-05",
                    null
                ],
                [
                    "2025-07-06",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-12",
                    null
                ],
                [
                    "2025-07-13",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-19",
                    null
                ],
                [
                    "2025-07-20",
                    null
                ],
                [
                    "2025-07-21",
                    30.99
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-26",
                    null
                ],
                [
                    "2025-07-27",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-02",
                    null
                ],
                [
                    "2025-08-03",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    34.84
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-09",
                    null
                ],
                [
                    "2025-08-10",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-16",
                    30.34
                ],
                [
                    "2025-08-17",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ],
                [
                    "2025-08-21",
                    31.88
                ],
                [
                    "2025-08-22",
                    null
                ],
                [
                    "2025-08-23",
                    null
                ],
                [
                    "2025-08-24",
                    null
                ],
                [
                    "2025-08-25",
                    null
                ],
                [
                    "2025-08-26",
                    null
                ],
                [
                    "2025-08-27",
                    null
                ],
                [
                    "2025-08-28",
                    null
                ],
                [
                    "2025-08-29",
                    null
                ],
                [
                    "2025-08-30",
                    null
                ],
                [
                    "2025-08-31",
                    null
                ],
                [
                    "2025-09-01",
                    null
                ],
                [
                    "2025-09-02",
                    null
                ],
                [
                    "2025-09-03",
                    null
                ],
                [
                    "2025-09-04",
                    null
                ],
                [
                    "2025-09-05",
                    null
                ],
                [
                    "2025-09-06",
                    null
                ],
                [
                    "2025-09-07",
                    null
                ],
                [
                    "2025-09-08",
                    null
                ],
                [
                    "2025-09-09",
                    null
                ],
                [
                    "2025-09-10",
                    22.96
                ],
                [
                    "2025-09-11",
                    null
                ],
                [
                    "2025-09-12",
                    null
                ],
                [
                    "2025-09-13",
                    null
                ],
                [
                    "2025-09-14",
                    null
                ],
                [
                    "2025-09-15",
                    null
                ],
                [
                    "2025-09-16",
                    null
                ],
                [
                    "2025-09-17",
                    null
                ],
                [
                    "2025-09-18",
                    null
                ],
                [
                    "2025-09-19",
                    null
                ],
                [
                    "2025-09-20",
                    null
                ],
                [
                    "2025-09-21",
                    null
                ],
                [
                    "2025-09-22",
                    null
                ],
                [
                    "2025-09-23",
                    null
                ],
                [
                    "2025-09-24",
                    null
                ],
                [
                    "2025-09-25",
                    null
                ],
                [
                    "2025-09-26",
                    null
                ],
                [
                    "2025-09-27",
                    null
                ],
                [
                    "2025-09-28",
                    null
                ],
                [
                    "2025-09-29",
                    null
                ],
                [
                    "2025-09-30",
                    null
                ],
                [
                    "2025-10-01",
                    null
                ],
                [
                    "2025-10-02",
                    null
                ],
                [
                    "2025-10-03",
                    null
                ],
                [
                    "2025-10-04",
                    null
                ],
                [
                    "2025-10-05",
                    27.51
                ],
                [
                    "2025-10-06",
                    null
                ],
                [
                    "2025-10-07",
                    null
                ],
                [
                    "2025-10-08",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "dashed",
                "color": "#1890ff"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u5cf0\u70b9",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 14,
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-05",
                    null
                ],
                [
                    "2025-07-06",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-12",
                    null
                ],
                [
                    "2025-07-13",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-19",
                    null
                ],
                [
                    "2025-07-20",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-26",
                    null
                ],
                [
                    "2025-07-27",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-02",
                    null
                ],
                [
                    "2025-08-03",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    34.84
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-09",
                    null
                ],
                [
                    "2025-08-10",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-16",
                    null
                ],
                [
                    "2025-08-17",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ],
                [
                    "2025-08-21",
                    31.88
                ],
                [
                    "2025-08-22",
                    null
                ],
                [
                    "2025-08-23",
                    null
                ],
                [
                    "2025-08-24",
                    null
                ],
                [
                    "2025-08-25",
                    null
                ],
                [
                    "2025-08-26",
                    null
                ],
                [
                    "2025-08-27",
                    null
                ],
                [
                    "2025-08-28",
                    null
                ],
                [
                    "2025-08-29",
                    null
                ],
                [
                    "2025-08-30",
                    null
                ],
                [
                    "2025-08-31",
                    null
                ],
                [
                    "2025-09-01",
                    null
                ],
                [
                    "2025-09-02",
                    null
                ],
                [
                    "2025-09-03",
                    null
                ],
                [
                    "2025-09-04",
                    null
                ],
                [
                    "2025-09-05",
                    null
                ],
                [
                    "2025-09-06",
                    null
                ],
                [
                    "2025-09-07",
                    null
                ],
                [
                    "2025-09-08",
                    null
                ],
                [
                    "2025-09-09",
                    null
                ],
                [
                    "2025-09-10",
                    null
                ],
                [
                    "2025-09-11",
                    null
                ],
                [
                    "2025-09-12",
                    null
                ],
                [
                    "2025-09-13",
                    null
                ],
                [
                    "2025-09-14",
                    null
                ],
                [
                    "2025-09-15",
                    null
                ],
                [
                    "2025-09-16",
                    null
                ],
                [
                    "2025-09-17",
                    null
                ],
                [
                    "2025-09-18",
                    null
                ],
                [
                    "2025-09-19",
                    null
                ],
                [
                    "2025-09-20",
                    null
                ],
                [
                    "2025-09-21",
                    null
                ],
                [
                    "2025-09-22",
                    null
                ],
                [
                    "2025-09-23",
                    null
                ],
                [
                    "2025-09-24",
                    null
                ],
                [
                    "2025-09-25",
                    null
                ],
                [
                    "2025-09-26",
                    null
                ],
                [
                    "2025-09-27",
                    null
                ],
                [
                    "2025-09-28",
                    null
                ],
                [
                    "2025-09-29",
                    null
                ],
                [
                    "2025-09-30",
                    null
                ],
                [
                    "2025-10-01",
                    null
                ],
                [
                    "2025-10-02",
                    null
                ],
                [
                    "2025-10-03",
                    null
                ],
                [
                    "2025-10-04",
                    null
                ],
                [
                    "2025-10-05",
                    27.51
                ],
                [
                    "2025-10-06",
                    null
                ],
                [
                    "2025-10-07",
                    null
                ],
                [
                    "2025-10-08",
                    null
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff6b6b",
                "borderColor": "#ffffff",
                "borderWidth": 1
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u5cf0\u70b9\u8fde\u7ebf",
            "connectNulls": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-05",
                    null
                ],
                [
                    "2025-07-06",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-12",
                    null
                ],
                [
                    "2025-07-13",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-19",
                    null
                ],
                [
                    "2025-07-20",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-26",
                    null
                ],
                [
                    "2025-07-27",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-02",
                    null
                ],
                [
                    "2025-08-03",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    34.84
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-09",
                    null
                ],
                [
                    "2025-08-10",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-16",
                    null
                ],
                [
                    "2025-08-17",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ],
                [
                    "2025-08-21",
                    31.88
                ],
                [
                    "2025-08-22",
                    null
                ],
                [
                    "2025-08-23",
                    null
                ],
                [
                    "2025-08-24",
                    null
                ],
                [
                    "2025-08-25",
                    null
                ],
                [
                    "2025-08-26",
                    null
                ],
                [
                    "2025-08-27",
                    null
                ],
                [
                    "2025-08-28",
                    null
                ],
                [
                    "2025-08-29",
                    null
                ],
                [
                    "2025-08-30",
                    null
                ],
                [
                    "2025-08-31",
                    null
                ],
                [
                    "2025-09-01",
                    null
                ],
                [
                    "2025-09-02",
                    null
                ],
                [
                    "2025-09-03",
                    null
                ],
                [
                    "2025-09-04",
                    null
                ],
                [
                    "2025-09-05",
                    null
                ],
                [
                    "2025-09-06",
                    null
                ],
                [
                    "2025-09-07",
                    null
                ],
                [
                    "2025-09-08",
                    null
                ],
                [
                    "2025-09-09",
                    null
                ],
                [
                    "2025-09-10",
                    null
                ],
                [
                    "2025-09-11",
                    null
                ],
                [
                    "2025-09-12",
                    null
                ],
                [
                    "2025-09-13",
                    null
                ],
                [
                    "2025-09-14",
                    null
                ],
                [
                    "2025-09-15",
                    null
                ],
                [
                    "2025-09-16",
                    null
                ],
                [
                    "2025-09-17",
                    null
                ],
                [
                    "2025-09-18",
                    null
                ],
                [
                    "2025-09-19",
                    null
                ],
                [
                    "2025-09-20",
                    null
                ],
                [
                    "2025-09-21",
                    null
                ],
                [
                    "2025-09-22",
                    null
                ],
                [
                    "2025-09-23",
                    null
                ],
                [
                    "2025-09-24",
                    null
                ],
                [
                    "2025-09-25",
                    null
                ],
                [
                    "2025-09-26",
                    null
                ],
                [
                    "2025-09-27",
                    null
                ],
                [
                    "2025-09-28",
                    null
                ],
                [
                    "2025-09-29",
                    null
                ],
                [
                    "2025-09-30",
                    null
                ],
                [
                    "2025-10-01",
                    null
                ],
                [
                    "2025-10-02",
                    null
                ],
                [
                    "2025-10-03",
                    null
                ],
                [
                    "2025-10-04",
                    null
                ],
                [
                    "2025-10-05",
                    27.51
                ],
                [
                    "2025-10-06",
                    null
                ],
                [
                    "2025-10-07",
                    null
                ],
                [
                    "2025-10-08",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff6b6b"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u8c37\u70b9",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 14,
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-05",
                    null
                ],
                [
                    "2025-07-06",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-12",
                    null
                ],
                [
                    "2025-07-13",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-19",
                    null
                ],
                [
                    "2025-07-20",
                    null
                ],
                [
                    "2025-07-21",
                    30.99
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-26",
                    null
                ],
                [
                    "2025-07-27",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-02",
                    null
                ],
                [
                    "2025-08-03",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-09",
                    null
                ],
                [
                    "2025-08-10",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-16",
                    30.34
                ],
                [
                    "2025-08-17",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ],
                [
                    "2025-08-21",
                    null
                ],
                [
                    "2025-08-22",
                    null
                ],
                [
                    "2025-08-23",
                    null
                ],
                [
                    "2025-08-24",
                    null
                ],
                [
                    "2025-08-25",
                    null
                ],
                [
                    "2025-08-26",
                    null
                ],
                [
                    "2025-08-27",
                    null
                ],
                [
                    "2025-08-28",
                    null
                ],
                [
                    "2025-08-29",
                    null
                ],
                [
                    "2025-08-30",
                    null
                ],
                [
                    "2025-08-31",
                    null
                ],
                [
                    "2025-09-01",
                    null
                ],
                [
                    "2025-09-02",
                    null
                ],
                [
                    "2025-09-03",
                    null
                ],
                [
                    "2025-09-04",
                    null
                ],
                [
                    "2025-09-05",
                    null
                ],
                [
                    "2025-09-06",
                    null
                ],
                [
                    "2025-09-07",
                    null
                ],
                [
                    "2025-09-08",
                    null
                ],
                [
                    "2025-09-09",
                    null
                ],
                [
                    "2025-09-10",
                    22.96
                ],
                [
                    "2025-09-11",
                    null
                ],
                [
                    "2025-09-12",
                    null
                ],
                [
                    "2025-09-13",
                    null
                ],
                [
                    "2025-09-14",
                    null
                ],
                [
                    "2025-09-15",
                    null
                ],
                [
                    "2025-09-16",
                    null
                ],
                [
                    "2025-09-17",
                    null
                ],
                [
                    "2025-09-18",
                    null
                ],
                [
                    "2025-09-19",
                    null
                ],
                [
                    "2025-09-20",
                    null
                ],
                [
                    "2025-09-21",
                    null
                ],
                [
                    "2025-09-22",
                    null
                ],
                [
                    "2025-09-23",
                    null
                ],
                [
                    "2025-09-24",
                    null
                ],
                [
                    "2025-09-25",
                    null
                ],
                [
                    "2025-09-26",
                    null
                ],
                [
                    "2025-09-27",
                    null
                ],
                [
                    "2025-09-28",
                    null
                ],
                [
                    "2025-09-29",
                    null
                ],
                [
                    "2025-09-30",
                    null
                ],
                [
                    "2025-10-01",
                    null
                ],
                [
                    "2025-10-02",
                    null
                ],
                [
                    "2025-10-03",
                    null
                ],
                [
                    "2025-10-04",
                    null
                ],
                [
                    "2025-10-05",
                    null
                ],
                [
                    "2025-10-06",
                    null
                ],
                [
                    "2025-10-07",
                    null
                ],
                [
                    "2025-10-08",
                    null
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4ecdc4",
                "borderColor": "#ffffff",
                "borderWidth": 1
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u8c37\u70b9\u8fde\u7ebf",
            "connectNulls": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-05",
                    null
                ],
                [
                    "2025-07-06",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-12",
                    null
                ],
                [
                    "2025-07-13",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-19",
                    null
                ],
                [
                    "2025-07-20",
                    null
                ],
                [
                    "2025-07-21",
                    30.99
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-26",
                    null
                ],
                [
                    "2025-07-27",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-02",
                    null
                ],
                [
                    "2025-08-03",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-09",
                    null
                ],
                [
                    "2025-08-10",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-16",
                    30.34
                ],
                [
                    "2025-08-17",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ],
                [
                    "2025-08-21",
                    null
                ],
                [
                    "2025-08-22",
                    null
                ],
                [
                    "2025-08-23",
                    null
                ],
                [
                    "2025-08-24",
                    null
                ],
                [
                    "2025-08-25",
                    null
                ],
                [
                    "2025-08-26",
                    null
                ],
                [
                    "2025-08-27",
                    null
                ],
                [
                    "2025-08-28",
                    null
                ],
                [
                    "2025-08-29",
                    null
                ],
                [
                    "2025-08-30",
                    null
                ],
                [
                    "2025-08-31",
                    null
                ],
                [
                    "2025-09-01",
                    null
                ],
                [
                    "2025-09-02",
                    null
                ],
                [
                    "2025-09-03",
                    null
                ],
                [
                    "2025-09-04",
                    null
                ],
                [
                    "2025-09-05",
                    null
                ],
                [
                    "2025-09-06",
                    null
                ],
                [
                    "2025-09-07",
                    null
                ],
                [
                    "2025-09-08",
                    null
                ],
                [
                    "2025-09-09",
                    null
                ],
                [
                    "2025-09-10",
                    22.96
                ],
                [
                    "2025-09-11",
                    null
                ],
                [
                    "2025-09-12",
                    null
                ],
                [
                    "2025-09-13",
                    null
                ],
                [
                    "2025-09-14",
                    null
                ],
                [
                    "2025-09-15",
                    null
                ],
                [
                    "2025-09-16",
                    null
                ],
                [
                    "2025-09-17",
                    null
                ],
                [
                    "2025-09-18",
                    null
                ],
                [
                    "2025-09-19",
                    null
                ],
                [
                    "2025-09-20",
                    null
                ],
                [
                    "2025-09-21",
                    null
                ],
                [
                    "2025-09-22",
                    null
                ],
                [
                    "2025-09-23",
                    null
                ],
                [
                    "2025-09-24",
                    null
                ],
                [
                    "2025-09-25",
                    null
                ],
                [
                    "2025-09-26",
                    null
                ],
                [
                    "2025-09-27",
                    null
                ],
                [
                    "2025-09-28",
                    null
                ],
                [
                    "2025-09-29",
                    null
                ],
                [
                    "2025-09-30",
                    null
                ],
                [
                    "2025-10-01",
                    null
                ],
                [
                    "2025-10-02",
                    null
                ],
                [
                    "2025-10-03",
                    null
                ],
                [
                    "2025-10-04",
                    null
                ],
                [
                    "2025-10-05",
                    null
                ],
                [
                    "2025-10-06",
                    null
                ],
                [
                    "2025-10-07",
                    null
                ],
                [
                    "2025-10-08",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#4ecdc4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                {
                    "value": 917026,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1085929,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 914629,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1175400,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1108944,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 883707,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 976472,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 980331,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1081137,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 953709,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1102698,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 951722,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 999295,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 994081,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1158375,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1182272,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1135544,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 829327,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 940373,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1176393,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1137039,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1094318,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1105939,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1067138,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 880132,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 833306,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 931813,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1177865,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1136048,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 801701,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1062451,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1127837,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 880131,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1078058,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 858650,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 961225,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 841291,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1198774,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1088255,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 911043,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1173790,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1029690,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 833568,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 915457,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 835337,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 924782,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1179245,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1047974,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1025994,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 828401,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1011089,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 900448,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 946037,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1088528,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 807737,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 887194,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 830741,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1038555,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1091382,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 914137,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1192154,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1078463,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 897425,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1011694,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1111699,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1096341,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1175490,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1138428,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 838033,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 852418,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 982981,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 882704,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1142871,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1147806,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 856117,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 910430,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 938403,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 826633,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 801858,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 884715,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 805069,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 878147,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 877643,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 820916,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 853893,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1125405,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 885197,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 812995,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1015856,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 939883,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 820302,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1041331,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 916876,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 836396,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1009546,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 814470,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 939180,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 964457,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 1102298,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 1072587,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                }
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "ZigZag\u8d8b\u52bf\u7ebf",
                "\u5cf0\u70b9",
                "\u5cf0\u70b9\u8fde\u7ebf",
                "\u8c37\u70b9",
                "\u8c37\u70b9\u8fde\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter":                         function (params) {                            var k = null;                            for (var i = 0; i < params.length; i++) {                                if (params[i].seriesType === 'candlestick') { k = params[i]; break; }                            }                            if (!k && params.length) { k = params[0]; }                            if (!k) { return ''; }                            var idx = k.dataIndex || 0;                            var sdArr = window.stockDataForTooltip || [];                            var sd = sdArr[idx] || {};                            var v = Array.isArray(k.value) ? k.value : [];                            function num(n){ return (n == null || isNaN(n)) ? '-' : Number(n).toFixed(2); }                            var open = num(v[1]);                            var close = num(v[2]);                            var low = num(v[3]);                            var high = num(v[4]);                            var parts = [];                            parts.push('\u65e5\u671f: ' + (k.axisValue || ''));                            parts.push('\u5f00\u76d8: ' + open);                            parts.push('\u6536\u76d8: ' + close);                            parts.push('\u6700\u4f4e: ' + low);                            parts.push('\u6700\u9ad8: ' + high);                            parts.push('\u6210\u4ea4\u91cf: ' + (sd.volume != null ? Number(sd.volume).toLocaleString() : '-'));                            if (sd.is_zigzag_point) { parts.push('\u5cf0\u8c37\u70b9: ' + (sd.zigzag_point_type || '')); }                            parts.push('\u8d8b\u52bf\u72b6\u6001: ' + (sd.trend_status || '\u6682\u65e0'));                            return parts.join('<br/>');                        }                    ,
        "textStyle": {
            "color": "#000",
            "fontSize": 12
        },
        "backgroundColor": "rgba(245, 245, 245, 0.95)",
        "borderColor": "#ccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 20,
            "boundaryGap": false,
            "min": "dataMin",
            "max": "dataMax",
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-05",
                "2025-07-06",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-12",
                "2025-07-13",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-19",
                "2025-07-20",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-26",
                "2025-07-27",
                "2025-07-28",
                "2025-07-29",
                "2025-07-30",
                "2025-07-31",
                "2025-08-01",
                "2025-08-02",
                "2025-08-03",
                "2025-08-04",
                "2025-08-05",
                "2025-08-06",
                "2025-08-07",
                "2025-08-08",
                "2025-08-09",
                "2025-08-10",
                "2025-08-11",
                "2025-08-12",
                "2025-08-13",
                "2025-08-14",
                "2025-08-15",
                "2025-08-16",
                "2025-08-17",
                "2025-08-18",
                "2025-08-19",
                "2025-08-20",
                "2025-08-21",
                "2025-08-22",
                "2025-08-23",
                "2025-08-24",
                "2025-08-25",
                "2025-08-26",
                "2025-08-27",
                "2025-08-28",
                "2025-08-29",
                "2025-08-30",
                "2025-08-31",
                "2025-09-01",
                "2025-09-02",
                "2025-09-03",
                "2025-09-04",
                "2025-09-05",
                "2025-09-06",
                "2025-09-07",
                "2025-09-08",
                "2025-09-09",
                "2025-09-10",
                "2025-09-11",
                "2025-09-12",
                "2025-09-13",
                "2025-09-14",
                "2025-09-15",
                "2025-09-16",
                "2025-09-17",
                "2025-09-18",
                "2025-09-19",
                "2025-09-20",
                "2025-09-21",
                "2025-09-22",
                "2025-09-23",
                "2025-09-24",
                "2025-09-25",
                "2025-09-26",
                "2025-09-27",
                "2025-09-28",
                "2025-09-29",
                "2025-09-30",
                "2025-10-01",
                "2025-10-02",
                "2025-10-03",
                "2025-10-04",
                "2025-10-05",
                "2025-10-06",
                "2025-10-07",
                "2025-10-08"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-05",
                "2025-07-06",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-12",
                "2025-07-13",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-19",
                "2025-07-20",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-26",
                "2025-07-27",
                "2025-07-28",
                "2025-07-29",
                "2025-07-30",
                "2025-07-31",
                "2025-08-01",
                "2025-08-02",
                "2025-08-03",
                "2025-08-04",
                "2025-08-05",
                "2025-08-06",
                "2025-08-07",
                "2025-08-08",
                "2025-08-09",
                "2025-08-10",
                "2025-08-11",
                "2025-08-12",
                "2025-08-13",
                "2025-08-14",
                "2025-08-15",
                "2025-08-16",
                "2025-08-17",
                "2025-08-18",
                "2025-08-19",
                "2025-08-20",
                "2025-08-21",
                "2025-08-22",
                "2025-08-23",
                "2025-08-24",
                "2025-08-25",
                "2025-08-26",
                "2025-08-27",
                "2025-08-28",
                "2025-08-29",
                "2025-08-30",
                "2025-08-31",
                "2025-09-01",
                "2025-09-02",
                "2025-09-03",
                "2025-09-04",
                "2025-09-05",
                "2025-09-06",
                "2025-09-07",
                "2025-09-08",
                "2025-09-09",
                "2025-09-10",
                "2025-09-11",
                "2025-09-12",
                "2025-09-13",
                "2025-09-14",
                "2025-09-15",
                "2025-09-16",
                "2025-09-17",
                "2025-09-18",
                "2025-09-19",
                "2025-09-20",
                "2025-09-21",
                "2025-09-22",
                "2025-09-23",
                "2025-09-24",
                "2025-09-25",
                "2025-09-26",
                "2025-09-27",
                "2025-09-28",
                "2025-09-29",
                "2025-09-30",
                "2025-10-01",
                "2025-10-02",
                "2025-10-03",
                "2025-10-04",
                "2025-10-05",
                "2025-10-06",
                "2025-10-07",
                "2025-10-08"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 3,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u80a1\u7968K\u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "left": "0",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": false,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "top": "90%",
            "filterMode": "filter"
        },
        [
            {
                "show": false,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "top": "90%",
                "filterMode": "filter"
            }
        ]
    ],
    "axisPointer": {
        "show": true,
        "type": "line",
        "link": [
            {
                "xAxisIndex": "all"
            }
        ],
        "label": {
            "show": true,
            "margin": 8,
            "backgroundColor": "#777",
            "valueAnimation": false
        },
        "triggerTooltip": true,
        "triggerOn": "mousemove|click"
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "right": "8%",
            "height": "60%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "16%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_29a548561044483f913b280312f94cd1.setOption(option_29a548561044483f913b280312f94cd1);
            window.addEventListener('resize', function(){
                chart_29a548561044483f913b280312f94cd1.resize();
            })
    </script>
</body>
</html>
