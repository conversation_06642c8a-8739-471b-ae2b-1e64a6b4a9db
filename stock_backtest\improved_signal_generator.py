from typing import List, Optional, Tuple
from .data_models import StockData, TradingSignal
from .signal_generator import ZigZagFibonacciSignalGenerator
from decimal import Decimal
import numpy as np


class ImprovedZigZagFibonacciSignalGenerator(ZigZagFibonacciSignalGenerator):
    """改进的ZigZag Fibonacci信号生成器"""

    def __init__(self, zigzag_threshold=0.05, fib_levels=None,
                 dynamic_threshold=False, volume_confirmation=True,
                 rsi_filter=True, macd_filter=True,
                 min_bandwidth=0.02, min_signal_interval=5,
                 atr_period=14, volume_multiplier=1.5,
                 rsi_overbought=70, rsi_oversold=30):
        """
        初始化改进的ZigZag Fibonacci信号生成器

        Args:
            zigzag_threshold: ZigZag的最小变化阈值（百分比）
            fib_levels: Fibonacci回撤水平列表
            dynamic_threshold: 是否使用动态阈值
            volume_confirmation: 是否需要成交量确认
            rsi_filter: 是否使用RSI过滤
            macd_filter: 是否使用MACD过滤
            min_bandwidth: 最小布林带宽度阈值
            min_signal_interval: 最小信号间隔（天数）
            atr_period: ATR计算周期
            volume_multiplier: 成交量倍数阈值
            rsi_overbought: RSI超买阈值
            rsi_oversold: RSI超卖阈值
        """
        super().__init__(zigzag_threshold, fib_levels)
        self.dynamic_threshold = dynamic_threshold
        self.volume_confirmation = volume_confirmation
        self.rsi_filter = rsi_filter
        self.macd_filter = macd_filter
        self.min_bandwidth = min_bandwidth
        self.min_signal_interval = min_signal_interval
        self.atr_period = atr_period
        self.volume_multiplier = volume_multiplier
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.last_signal_date = None

    def generate_signals(self, stock_data: List[StockData]) -> List[TradingSignal]:
        """生成改进的交易信号"""
        if len(stock_data) < 20:  # 需要足够的数据点
            return []

        # 计算ATR用于动态阈值和过滤
        atr_values = self._calculate_atr(stock_data, self.atr_period)

        # 计算ZigZag点
        if self.dynamic_threshold and atr_values:
            # 使用动态阈值计算ZigZag点
            zigzag_points = self._calculate_zigzag_dynamic(stock_data, atr_values)
        else:
            # 使用固定阈值计算ZigZag点
            zigzag_points = self._calculate_zigzag(stock_data)

        if len(zigzag_points) < 3:  # 需要至少3个ZigZag点来计算Fibonacci
            return []

        signals = []

        # 遍历股票数据，寻找Fibonacci回撤信号
        for i in range(len(stock_data)):
            current = stock_data[i]

            # 检查信号间隔
            if not self._is_signal_interval_valid(current.date):
                continue

            # 获取当前点之前的最近两个ZigZag点
            recent_zigzag = self._get_recent_zigzag_points(zigzag_points, i, 2)

            if len(recent_zigzag) >= 2:
                # 检查趋势强度
                if not self._check_trend_strength(recent_zigzag):
                    continue

                # 检查波动性
                if not self._check_volatility(atr_values, i):
                    continue

                # 检查布林带宽度
                if not self._check_bollinger_bandwidth(stock_data, i):
                    continue

                # 检查成交量确认
                if self.volume_confirmation and not self._check_volume_confirmation(stock_data, i):
                    continue

                # 检查RSI过滤
                if self.rsi_filter and not self._check_rsi_filter(stock_data, i):
                    continue

                # 检查MACD过滤
                if self.macd_filter and not self._check_macd_filter(stock_data, i):
                    continue

                # 生成Fibonacci信号
                signal = self._check_fibonacci_signal(current, recent_zigzag, i)
                if signal:
                    # 更新最后信号日期
                    self.last_signal_date = current.date
                    signals.append(signal)

        return signals

    def _calculate_atr(self, stock_data: List[StockData], period: int) -> List[Decimal]:
        """计算ATR(平均真实波幅)"""
        if len(stock_data) < period:
            return []

        atr_values = [Decimal(0)] * len(stock_data)

        for i in range(1, len(stock_data)):
            current = stock_data[i]
            previous = stock_data[i - 1]

            # 计算真实波幅(TR)
            tr1 = current.high - current.low
            tr2 = abs(current.high - previous.close)
            tr3 = abs(current.low - previous.close)
            tr = max(tr1, tr2, tr3)

            # 计算ATR
            if i < period:
                # 简单移动平均
                atr_values[i] = sum(
                    max(stock_data[j].high - stock_data[j].low,
                        abs(stock_data[j].high - stock_data[j - 1].close if j > 0 else 0),
                        abs(stock_data[j].low - stock_data[j - 1].close if j > 0 else 0))
                    for j in range(1, i + 1)
                ) / Decimal(i)
            else:
                # 指数移动平均
                atr_values[i] = (atr_values[i - 1] * Decimal(period - 1) + tr) / Decimal(period)

        return atr_values

    def _calculate_zigzag_dynamic(self, stock_data: List[StockData], atr_values: List[Decimal]) -> List[Tuple[int, Decimal, str]]:
        """
        使用动态阈值计算ZigZag点

        Returns:
            List of (index, price, type) where type is 'HIGH' or 'LOW'
        """
        zigzag_points = []

        if len(stock_data) < 3:
            return zigzag_points

        # 计算平均ATR作为基准
        valid_atr = [atr for atr in atr_values if atr > 0]
        if not valid_atr:
            return self._calculate_zigzag(stock_data)

        avg_atr = sum(valid_atr) / len(valid_atr)
        base_price = stock_data[0].close

        # 计算动态阈值 (ATR百分比)
        dynamic_threshold = avg_atr / base_price

        # 寻找第一个有效的高点或低点
        current_trend = None
        last_extreme_idx = 0
        last_extreme_high = stock_data[0].high
        last_extreme_low = stock_data[0].low

        for i in range(1, len(stock_data)):
            current_high = stock_data[i].high
            current_low = stock_data[i].low
            current_close = stock_data[i].close

            # 获取当前ATR值
            current_atr = atr_values[i] if i < len(atr_values) and atr_values[i] > 0 else avg_atr
            current_threshold = current_atr / current_close * Decimal(0.5)  # 调整系数

            if current_trend is None:
                # 初始化趋势判断
                if current_high > last_extreme_high:
                    # 价格创新高，开始上涨趋势
                    current_trend = 'UP'
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                elif current_low < last_extreme_low:
                    # 价格创新低，开始下跌趋势
                    current_trend = 'DOWN'
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 更新当前极值
                    if current_high > last_extreme_high:
                        last_extreme_high = current_high
                    if current_low < last_extreme_low:
                        last_extreme_low = current_low
                continue

            if current_trend == 'UP':
                # 上涨趋势中，寻找更高的高点或转折点
                if current_high > last_extreme_high:
                    # 创新高，更新高点
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 检查是否出现显著回调（从最高点回调超过阈值）
                    price_decline = (last_extreme_high - current_low) / last_extreme_high
                    if price_decline >= current_threshold:
                        # 趋势转为下跌，记录前一个高点
                        zigzag_points.append((last_extreme_idx, last_extreme_high, 'HIGH'))
                        current_trend = 'DOWN'
                        last_extreme_idx = i
                        last_extreme_high = current_high
                        last_extreme_low = current_low

            elif current_trend == 'DOWN':
                # 下跌趋势中，寻找更低的低点或转折点
                if current_low < last_extreme_low:
                    # 创新低，更新低点
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 检查是否出现显著反弹（从最低点反弹超过阈值）
                    price_rise = (current_high - last_extreme_low) / last_extreme_low
                    if price_rise >= current_threshold:
                        # 趋势转为上涨，记录前一个低点
                        zigzag_points.append((last_extreme_idx, last_extreme_low, 'LOW'))
                        current_trend = 'UP'
                        last_extreme_idx = i
                        last_extreme_high = current_high
                        last_extreme_low = current_low

        # 添加最后一个极值点
        if current_trend is not None:
            if current_trend == 'UP':
                zigzag_points.append((last_extreme_idx, last_extreme_high, 'HIGH'))
            else:
                zigzag_points.append((last_extreme_idx, last_extreme_low, 'LOW'))

        return zigzag_points

    def _check_trend_strength(self, zigzag_points: List[Tuple[int, Decimal, str]]) -> bool:
        """检查趋势强度"""
        if len(zigzag_points) < 3:
            return True  # 数据不足时不过滤

        # 计算最近两个波段的幅度
        point1 = zigzag_points[-3]
        point2 = zigzag_points[-2]
        point3 = zigzag_points[-1]

        # 计算波段幅度
        move1 = abs(point2[1] - point1[1]) / point1[1]  # 第一个波段幅度
        move2 = abs(point3[1] - point2[1]) / point2[1]  # 第二个波段幅度

        # 趋势强度判断：第二个波段幅度不小于第一个波段的50%
        return move2 >= move1 * Decimal(0.5)

    def _check_volatility(self, atr_values: List[Decimal], index: int) -> bool:
        """检查价格波动性"""
        if not atr_values or index >= len(atr_values):
            return True  # 数据不足时不过滤

        current_atr = atr_values[index]
        if current_atr <= 0:
            return True

        # 计算平均ATR
        valid_atr = [atr for atr in atr_values[:index + 1] if atr > 0]
        if len(valid_atr) < 5:
            return True

        avg_atr = sum(valid_atr) / len(valid_atr)

        # 只在波动性高于平均值的50%时生成信号
        return current_atr >= avg_atr * Decimal(0.5)

    def _check_volume_confirmation(self, stock_data: List[StockData], index: int) -> bool:
        """检查成交量确认"""
        if index >= len(stock_data):
            return True

        current = stock_data[index]

        # 检查是否有成交量数据
        if current.volume <= 0:
            return True

        # 计算平均成交量
        start_idx = max(0, index - 20)
        volume_window = [data.volume for data in stock_data[start_idx:index + 1] if data.volume > 0]
        if len(volume_window) < 5:
            return True

        avg_volume = sum(volume_window) / len(volume_window)

        # 成交量需要是平均成交量的一定倍数
        return current.volume >= avg_volume * self.volume_multiplier

    def _check_rsi_filter(self, stock_data: List[StockData], index: int) -> bool:
        """RSI过滤"""
        if index >= len(stock_data):
            return True

        # 计算RSI
        rsi = self._calculate_rsi(stock_data, index, 14)
        if rsi is None:
            return True

        current = stock_data[index]

        # 获取前一个数据点
        prev = stock_data[index - 1] if index > 0 else None

        # 买入信号：RSI不在超买区域
        if prev and current.close > prev.close:  # 看涨信号
            return rsi <= self.rsi_overbought

        # 卖出信号：RSI不在超卖区域
        if prev and current.close < prev.close:  # 看跌信号
            return rsi >= self.rsi_oversold

        return True

    def _calculate_rsi(self, stock_data: List[StockData], index: int, period: int) -> Optional[Decimal]:
        """计算RSI"""
        if index < period:
            return None

        gains = []
        losses = []

        for i in range(index - period + 1, index + 1):
            if i > 0:
                change = stock_data[i].close - stock_data[i - 1].close
                if change > 0:
                    gains.append(change)
                    losses.append(Decimal(0))
                else:
                    gains.append(Decimal(0))
                    losses.append(abs(change))

        if not gains or not losses:
            return None

        avg_gain = sum(gains) / len(gains)
        avg_loss = sum(losses) / len(losses)

        if avg_loss == 0:
            return Decimal(100)

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _check_macd_filter(self, stock_data: List[StockData], index: int) -> bool:
        """MACD过滤"""
        if index >= len(stock_data):
            return True

        current = stock_data[index]

        # 检查是否有MACD数据
        if current.macd is None or current.macd_signal is None:
            return True

        # 买入信号：MACD在零轴上方且MACD线在信号线上方
        if current.macd > 0 and current.macd > current.macd_signal:
            return True

        # 卖出信号：MACD在零轴下方且MACD线在信号线下方
        if current.macd < 0 and current.macd < current.macd_signal:
            return True

        return False

    def _check_bollinger_bandwidth(self, stock_data: List[StockData], index: int) -> bool:
        """检查布林带宽度"""
        if index >= len(stock_data):
            return True

        current = stock_data[index]

        # 检查是否有布林带数据
        if current.bb_upper is None or current.bb_lower is None or current.bb_middle is None:
            return True

        # 计算布林带宽度百分比
        bandwidth = (current.bb_upper - current.bb_lower) / current.bb_middle

        # 只在布林带宽度大于最小阈值时生成信号
        return bandwidth >= self.min_bandwidth

    def _is_signal_interval_valid(self, current_date) -> bool:
        """检查信号间隔"""
        if self.last_signal_date is None:
            return True

        # 计算距离上次信号的天数
        days_since_last = (current_date - self.last_signal_date).days

        # 检查是否满足最小信号间隔要求
        return days_since_last >= self.min_signal_interval