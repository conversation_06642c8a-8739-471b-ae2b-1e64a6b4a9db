#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZigZag 点序列验证和修复工具
检测并修复连续的 HIGH 或 LOW 点问题
"""

from typing import List
from zigzag_analysis import StockData, ZigZagPoint

class ZigZagValidator:
    """ZigZag 点序列验证器"""
    
    def __init__(self):
        pass
    
    def validate_zigzag_sequence(self, stock_data: List[StockData]) -> dict:
        """
        验证 ZigZag 点序列的正确性
        
        Returns:
            dict: 包含验证结果和问题详情
        """
        zigzag_points = []
        
        # 提取所有 ZigZag 点
        for i, data in enumerate(stock_data):
            if data.is_zigzag_point:
                zigzag_points.append({
                    'index': i,
                    'date': data.date.strftime('%Y-%m-%d'),
                    'type': data.zigzag_point_type,
                    'price': float(data.high if data.zigzag_point_type == 'HIGH' else data.low),
                    'close': float(data.close),
                    'data_ref': data
                })
        
        # 检查连续性问题
        consecutive_issues = []
        for i in range(len(zigzag_points) - 1):
            current = zigzag_points[i]
            next_point = zigzag_points[i + 1]
            
            if current['type'] == next_point['type']:
                consecutive_issues.append({
                    'issue_type': 'consecutive_' + current['type'].lower(),
                    'point1': current,
                    'point2': next_point,
                    'missing_type': 'LOW' if current['type'] == 'HIGH' else 'HIGH'
                })
        
        return {
            'total_points': len(zigzag_points),
            'zigzag_points': zigzag_points,
            'consecutive_issues': consecutive_issues,
            'is_valid': len(consecutive_issues) == 0
        }
    
    def find_missing_points(self, stock_data: List[StockData], issue: dict) -> List[dict]:
        """
        在两个连续的同类型点之间寻找缺失的转折点
        """
        point1 = issue['point1']
        point2 = issue['point2']
        missing_type = issue['missing_type']
        
        start_idx = point1['index']
        end_idx = point2['index']
        
        candidates = []
        
        if missing_type == 'LOW':
            # 寻找最低点
            min_price = float('inf')
            min_idx = -1
            
            for i in range(start_idx + 1, end_idx):
                if float(stock_data[i].low) < min_price:
                    min_price = float(stock_data[i].low)
                    min_idx = i
            
            if min_idx != -1:
                candidates.append({
                    'index': min_idx,
                    'date': stock_data[min_idx].date.strftime('%Y-%m-%d'),
                    'type': 'LOW',
                    'price': min_price,
                    'close': float(stock_data[min_idx].close),
                    'reason': f'最低点在 {point1["date"]} 和 {point2["date"]} 之间'
                })
        
        else:  # missing_type == 'HIGH'
            # 寻找最高点
            max_price = 0
            max_idx = -1
            
            for i in range(start_idx + 1, end_idx):
                if float(stock_data[i].high) > max_price:
                    max_price = float(stock_data[i].high)
                    max_idx = i
            
            if max_idx != -1:
                candidates.append({
                    'index': max_idx,
                    'date': stock_data[max_idx].date.strftime('%Y-%m-%d'),
                    'type': 'HIGH',
                    'price': max_price,
                    'close': float(stock_data[max_idx].close),
                    'reason': f'最高点在 {point1["date"]} 和 {point2["date"]} 之间'
                })
        
        return candidates
    
    def generate_validation_report(self, stock_data: List[StockData], symbol: str = "") -> str:
        """生成详细的验证报告"""
        validation_result = self.validate_zigzag_sequence(stock_data)
        
        report = []
        report.append("=" * 80)
        report.append(f"ZigZag 点序列验证报告 - {symbol}")
        report.append("=" * 80)
        
        report.append(f"总数据点: {len(stock_data)}")
        report.append(f"ZigZag 点数量: {validation_result['total_points']}")
        report.append(f"序列有效性: {'✅ 有效' if validation_result['is_valid'] else '❌ 无效'}")
        
        if validation_result['zigzag_points']:
            report.append("\nZigZag 点序列:")
            report.append("-" * 80)
            for i, point in enumerate(validation_result['zigzag_points']):
                report.append(f"{i+1:2d}. {point['date']} | {point['type']:4s} | "
                            f"价格: {point['price']:6.2f} | 收盘: {point['close']:6.2f}")
        
        if validation_result['consecutive_issues']:
            report.append(f"\n❌ 发现 {len(validation_result['consecutive_issues'])} 个连续性问题:")
            report.append("-" * 80)
            
            for i, issue in enumerate(validation_result['consecutive_issues']):
                p1 = issue['point1']
                p2 = issue['point2']
                report.append(f"\n问题 {i+1}: 连续的 {p1['type']} 点")
                report.append(f"   {p1['date']} ({p1['type']}: {p1['price']:.2f}) -> "
                            f"{p2['date']} ({p2['type']}: {p2['price']:.2f})")
                report.append(f"   缺失: {issue['missing_type']} 点")
                
                # 寻找可能的缺失点
                candidates = self.find_missing_points(stock_data, issue)
                if candidates:
                    report.append(f"   建议的 {issue['missing_type']} 点:")
                    for candidate in candidates:
                        report.append(f"     - {candidate['date']}: {candidate['price']:.2f} ({candidate['reason']})")
                else:
                    report.append(f"   未找到明显的 {issue['missing_type']} 点候选")
        
        return "\n".join(report)

def validate_stock_zigzag(stock_code: str, start_date: str, end_date: str):
    """验证指定股票的 ZigZag 点序列"""
    try:
        from zigzag_analysis import get_stock_data, dataframe_to_stock_data, ZigZagAnalyzer
        
        print(f"正在验证 {stock_code} 的 ZigZag 点序列...")
        
        # 获取数据
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data_list = dataframe_to_stock_data(df, stock_code)
        
        # 执行 ZigZag 分析
        analyzer = ZigZagAnalyzer(
            base_threshold=0.05,
            dynamic_threshold=True,
            trend_lookback_points=4
        )
        
        analyzed_data = analyzer.calculate_and_analyze(stock_data_list)
        
        # 验证结果
        validator = ZigZagValidator()
        report = validator.generate_validation_report(analyzed_data, stock_code)
        
        print(report)
        
        # 保存报告
        report_file = f"{stock_code}_zigzag_validation_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n验证报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"验证失败: {str(e)}")

if __name__ == "__main__":
    # 验证 000002 万科A
    validate_stock_zigzag("000002", "20240301", "20240420")
