from typing import List, <PERSON><PERSON>
from decimal import Decimal
import pandas as pd
from .data_models import StockData

class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def calculate_ema(prices: List[Decimal], period: int) -> List[Decimal]:
        """计算指数移动平均线"""
        if len(prices) < period:
            return [None] * len(prices)
        
        ema = []
        multiplier = Decimal(2) / Decimal(period + 1)
        
        # 第一个EMA值使用SMA
        sma = sum(prices[:period]) / Decimal(period)
        ema.extend([None] * (period - 1))
        ema.append(sma)
        
        for i in range(period, len(prices)):
            ema_value = (prices[i] * multiplier) + (ema[-1] * (Decimal(1) - multiplier))
            ema.append(ema_value)
        
        return ema
    
    @staticmethod
    def calculate_macd(prices: List[Decimal], fast=12, slow=26, signal=9) -> Tuple[List[Decimal], List[Decimal], List[Decimal]]:
        """计算MACD指标"""
        ema_fast = TechnicalIndicators.calculate_ema(prices, fast)
        ema_slow = TechnicalIndicators.calculate_ema(prices, slow)
        
        macd_line = []
        for i in range(len(prices)):
            if ema_fast[i] is not None and ema_slow[i] is not None:
                macd_line.append(ema_fast[i] - ema_slow[i])
            else:
                macd_line.append(None)
        
        # 计算信号线
        valid_macd = [x for x in macd_line if x is not None]
        signal_line = TechnicalIndicators.calculate_ema(valid_macd, signal)
        
        # 补齐信号线长度
        signal_full = [None] * (len(macd_line) - len(signal_line)) + signal_line
        
        # 计算柱状图
        histogram = []
        for i in range(len(macd_line)):
            if macd_line[i] is not None and signal_full[i] is not None:
                histogram.append(macd_line[i] - signal_full[i])
            else:
                histogram.append(None)
        
        return macd_line, signal_full, histogram
    
    @staticmethod
    def calculate_bollinger_bands(prices: List[Decimal], period=20, std_dev=2) -> Tuple[List[Decimal], List[Decimal], List[Decimal]]:
        """计算布林线"""
        if len(prices) < period:
            return [None] * len(prices), [None] * len(prices), [None] * len(prices)
        
        upper_band = []
        middle_band = []
        lower_band = []
        
        for i in range(len(prices)):
            if i < period - 1:
                upper_band.append(None)
                middle_band.append(None)
                lower_band.append(None)
            else:
                window = prices[i - period + 1:i + 1]
                mean = sum(window) / Decimal(period)
                variance = sum((x - mean) ** 2 for x in window) / Decimal(period)
                std = variance.sqrt()
                
                middle_band.append(mean)
                upper_band.append(mean + Decimal(std_dev) * std)
                lower_band.append(mean - Decimal(std_dev) * std)
        
        return upper_band, middle_band, lower_band