#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    from stock_backtest.data_models import StockData
    from datetime import datetime
    from decimal import Decimal
    
    print("导入成功!")
    
    # 测试创建 StockData 对象
    test_data = StockData(
        symbol="TEST001",
        date=datetime.now(),
        open=Decimal("10.0"),
        high=Decimal("10.5"),
        low=Decimal("9.8"),
        close=Decimal("10.2"),
        volume=1000000
    )
    
    print(f"创建 StockData 对象成功: {test_data.symbol}")
    print(f"is_zigzag_point: {test_data.is_zigzag_point}")
    print(f"zigzag_point_type: {test_data.zigzag_point_type}")
    print(f"trend_status: {test_data.trend_status}")
    
    # 测试设置字段
    test_data.is_zigzag_point = True
    test_data.zigzag_point_type = "HIGH"
    test_data.trend_status = "UP"
    
    print(f"设置后 - is_zigzag_point: {test_data.is_zigzag_point}")
    print(f"设置后 - zigzag_point_type: {test_data.zigzag_point_type}")
    print(f"设置后 - trend_status: {test_data.trend_status}")
    
    print("所有测试通过!")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
