#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataclasses import dataclass
from decimal import Decimal, getcontext
from typing import Optional, List
from datetime import datetime, timedelta

getcontext().prec = 28

@dataclass
class StockData:
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None  # 'HIGH' or 'LOW'
    trend_status: Optional[str] = None       # 'UP' or 'DOWN'

@dataclass
class ZigZagPoint:
    date: str
    point_type: str      # 'HIGH'/'LOW'/'PENDING_HIGH'/'PENDING_LOW'
    price: Decimal       # 对于 HIGH 用最高价；LOW 用最低价
    source: str = "zz"   # 你可存来源/备注

@dataclass
class ZigZagState:
    symbol: str
    atr_period: int = 14
    atr_multiplier: Decimal = Decimal('1.5')
    min_price_move: Decimal = Decimal('0')
    min_trend_bars: int = 1
    prev_close: Optional[Decimal] = None
    atr: Optional[Decimal] = None
    atr_count: int = 0
    tr_sum: Decimal = Decimal('0')
    trend: Optional[str] = None
    bars_in_trend: int = 0
    extreme_high: Optional[Decimal] = None
    extreme_high_date: Optional[str] = None
    extreme_low: Optional[Decimal] = None
    extreme_low_date: Optional[str] = None
    confirmed_points: List[ZigZagPoint] = None
    pending_point: Optional[ZigZagPoint] = None

    def __post_init__(self):
        if self.confirmed_points is None:
            self.confirmed_points = []

def _update_atr(state: ZigZagState, bar: StockData):
    high, low, close = bar.high, bar.low, bar.close
    if state.prev_close is None:
        tr = high - low
    else:
        tr = max(high - low, abs(high - state.prev_close), abs(low - state.prev_close))

    if state.atr_count < state.atr_period:
        state.tr_sum += tr
        state.atr_count += 1
        if state.atr_count == state.atr_period:
            state.atr = (state.tr_sum / Decimal(state.atr_period))
    else:
        state.atr = ((state.atr * Decimal(state.atr_period - 1)) + tr) / Decimal(state.atr_period)

    state.prev_close = close

def _update_trend_status_from_point(stock_data_list: List[StockData], from_date: str, trend: str, symbol: str):
    """从指定日期开始更新趋势状态"""
    if not stock_data_list:
        return
    
    start_idx = -1
    for i in range(len(stock_data_list) - 1, -1, -1):
        stock_date_str = stock_data_list[i].date.strftime('%Y-%m-%d')
        if stock_date_str == from_date and stock_data_list[i].symbol == symbol:
            start_idx = i
            break
    
    if start_idx >= 0:
        for i in range(start_idx, len(stock_data_list)):
            if stock_data_list[i].symbol == symbol:
                stock_data_list[i].trend_status = trend

def _clear_zigzag_point_marking(stock_data_list: List[StockData], date: str, symbol: str):
    """清除指定日期的zigzag点标记"""
    for stock_data in stock_data_list:
        stock_date_str = stock_data.date.strftime('%Y-%m-%d')
        if stock_date_str == date and stock_data.symbol == symbol:
            stock_data.is_zigzag_point = False
            stock_data.zigzag_point_type = None
            break

def _confirm_point(state: ZigZagState, point_type: str, date: str, price: Decimal, stock_data_list: List[StockData] = None):
    """确认拐点，避免连续的高点或低点"""
    # 检查是否会产生连续的同类型点
    if state.confirmed_points and state.confirmed_points[-1].point_type == point_type:
        last = state.confirmed_points[-1]
        
        should_replace = False
        if point_type == "HIGH":
            should_replace = price > last.price
        else:  # LOW
            should_replace = price < last.price
        
        if should_replace:
            # 先清除旧点的标记
            if stock_data_list:
                _clear_zigzag_point_marking(stock_data_list, last.date, state.symbol)
            
            # 更新为更极端的点
            state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
            print(f"    替换{point_type}点: {last.date} {last.price} -> {date} {price}")
        else:
            # 不替换，直接返回
            print(f"    跳过较弱的{point_type}点: {date} {price} (当前最强: {last.date} {last.price})")
            return
    else:
        # 添加新的拐点
        state.confirmed_points.append(ZigZagPoint(date, point_type, price))
        print(f"    确认新{point_type}点: {date} {price}")

    # 更新对应的 StockData 对象
    if stock_data_list:
        # 标记新的zigzag点
        for stock_data in stock_data_list:
            stock_date_str = stock_data.date.strftime('%Y-%m-%d')
            if stock_date_str == date and stock_data.symbol == state.symbol:
                stock_data.is_zigzag_point = True
                stock_data.zigzag_point_type = point_type
                break
        
        # 更新趋势状态
        if len(state.confirmed_points) >= 2:
            last_two = state.confirmed_points[-2:]
            current_trend = None
            if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
                current_trend = "UP"
            elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
                current_trend = "DOWN"
            
            if current_trend:
                _update_trend_status_from_point(stock_data_list, date, current_trend, state.symbol)
                print(f"    更新趋势状态为: {current_trend}")

    state.pending_point = None

def update_state(state: ZigZagState, bar: StockData, stock_data_list: List[StockData] = None) -> ZigZagState:
    _update_atr(state, bar)

    if state.atr is None:
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')
        if state.pending_point is None:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
                state.trend = "DOWN"
            else:
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
                state.trend = "UP"
        state.bars_in_trend += 1
        return state

    if state.trend is None:
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')

        rng = state.extreme_high - state.extreme_low
        if rng >= state.atr * state.atr_multiplier and rng >= state.min_price_move:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.trend = "DOWN"
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
            else:
                state.trend = "UP"
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
            state.bars_in_trend = 1
        else:
            state.bars_in_trend += 1
        return state

    state.bars_in_trend += 1

    if state.trend == "UP":
        if state.extreme_high is None or bar.high >= state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

        if (state.extreme_high - bar.low) >= state.atr * state.atr_multiplier and \
           (state.extreme_high - bar.low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            _confirm_point(state, "HIGH", state.extreme_high_date, state.extreme_high, stock_data_list)
            state.trend = "DOWN"
            state.bars_in_trend = 1
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

    elif state.trend == "DOWN":
        if state.extreme_low is None or bar.low <= state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

        if (bar.high - state.extreme_low) >= state.atr * state.atr_multiplier and \
           (bar.high - state.extreme_low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            _confirm_point(state, "LOW", state.extreme_low_date, state.extreme_low, stock_data_list)
            state.trend = "UP"
            state.bars_in_trend = 1
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d')
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

    return state

def create_test_data():
    """创建包含连续高点和低点的测试数据"""
    base_date = datetime(2025, 7, 1)
    prices = [
        (10.0, 10.5, 9.8, 10.2),   # 第1天
        (10.2, 10.8, 10.0, 10.6),  # 第2天
        (10.6, 11.2, 10.4, 11.0),  # 第3天 - 第一个高点
        (11.0, 11.5, 10.8, 11.3),  # 第4天 - 更高的高点（应该替换第3天）
        (11.3, 11.1, 10.5, 10.7),  # 第5天
        (10.7, 10.8, 9.8, 10.0),   # 第6天
        (10.0, 10.2, 9.3, 9.5),    # 第7天 - 第一个低点
        (9.5, 9.8, 9.0, 9.2),      # 第8天 - 更低的低点（应该替换第7天）
        (9.2, 10.0, 9.1, 9.8),     # 第9天
        (9.8, 10.5, 9.7, 10.3),    # 第10天
        (10.3, 11.0, 10.1, 10.8),  # 第11天
        (10.8, 11.8, 10.6, 11.5),  # 第12天 - 新的高点
    ]
    
    stock_data_list = []
    for i, (open_price, high_price, low_price, close_price) in enumerate(prices):
        date = base_date + timedelta(days=i)
        stock_data = StockData(
            symbol="TEST001",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list

if __name__ == "__main__":
    print("=== 测试优化后的ZigZag算法 ===")
    
    stock_data_list = create_test_data()
    state = ZigZagState(symbol="TEST001", atr_multiplier=Decimal('0.1'))  # 进一步降低阈值
    
    print("处理数据...")
    for i, bar in enumerate(stock_data_list):
        print(f"\n第{i+1}天 {bar.date.strftime('%Y-%m-%d')}: 高={bar.high} 低={bar.low}")
        state = update_state(state, bar, stock_data_list)

        # 显示状态信息
        print(f"  ATR: {state.atr}, 趋势: {state.trend}, 已走: {state.bars_in_trend}根")
        if state.pending_point:
            print(f"  Pending: {state.pending_point.point_type} {state.pending_point.price}")

        if bar.is_zigzag_point:
            price = bar.high if bar.zigzag_point_type == 'HIGH' else bar.low
            print(f"  *** ZigZag点: {bar.zigzag_point_type} {price} 趋势={bar.trend_status} ***")
    
    print(f"\n=== 最终结果 ===")
    print(f"总确认拐点数: {len(state.confirmed_points)}")
    for i, point in enumerate(state.confirmed_points):
        print(f"拐点{i+1}: {point.date} {point.point_type} {point.price}")
    
    # 验证没有连续的同类型点
    consecutive_count = 0
    for i in range(1, len(state.confirmed_points)):
        if state.confirmed_points[i].point_type == state.confirmed_points[i-1].point_type:
            consecutive_count += 1
    
    if consecutive_count == 0:
        print("✅ 没有连续的同类型点，优化成功！")
    else:
        print(f"❌ 发现 {consecutive_count} 个连续同类型点")
    
    print("\n=== 所有ZigZag点 ===")
    for i, data in enumerate(stock_data_list):
        if data.is_zigzag_point:
            price = data.high if data.zigzag_point_type == 'HIGH' else data.low
            print(f"第{i+1}天 {data.date.strftime('%Y-%m-%d')}: {data.zigzag_point_type} {price} 趋势={data.trend_status}")
