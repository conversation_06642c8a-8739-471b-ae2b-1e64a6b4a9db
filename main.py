import akshare as ak
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Optional, List
import pandas as pd
from stock_visualization import StockVisualizer


@dataclass
class StockData:
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    vol5: Optional[Decimal] = None
    vol20: Optional[Decimal] = None
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None  # 'HIGH' or 'LOW'
    trend_status: Optional[str] = None       # 'UP' or 'DOWN'

def get_stock_data(stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从 AKShare 获取股票历史数据
    :param stock_code: 股票代码 (如 "600000")
    :param start_date: 开始日期 (格式 "YYYYMMDD")
    :param end_date: 结束日期 (格式 "YYYYMMDD")
    :return: 包含 OHLCV 数据的 DataFrame
    """
    try:
        # 获取后复权数据（包含分红送股）
        df = ak.stock_zh_a_hist(
            symbol=stock_code, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            adjust="qfq"
        )
        
        # 规范列名
        df = df.rename(columns={
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume'
        })
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 计算成交量均线（5日与20日移动平均）
        df['vol5'] = df['volume'].rolling(window=5, min_periods=1).mean()
        df['vol20'] = df['volume'].rolling(window=20, min_periods=1).mean()

        return df[['date', 'open', 'high', 'low', 'close', 'volume', 'vol5', 'vol20']]
    
    except Exception as e:
        raise RuntimeError(f"获取 {stock_code} 数据失败: {str(e)}")

def dataframe_to_stock_data(df: pd.DataFrame, symbol: str) -> List[StockData]:
    """使用列表推导式优化，将 DataFrame 转换为 StockData 列表"""
    return [
        StockData(
            symbol=symbol,
            date=row.date.to_pydatetime(),
            open=Decimal(str(row.open)),
            high=Decimal(str(row.high)),
            low=Decimal(str(row.low)),
            close=Decimal(str(row.close)),
            volume=int(row.volume)
        )
        for row in df.itertuples()
    ]

def calculate_atr(data: List[StockData], period: int = 14) -> List[Optional[Decimal]]:
    atr_values = [None] * len(data)
    if not data:
        return atr_values

    trs: List[Decimal] = []
    running_sum = Decimal('0')

    for i in range(len(data)):
        if i == 0:
            tr = data[0].high - data[0].low
        else:
            prev_close = data[i-1].close
            tr = max(
                data[i].high - data[i].low,
                abs(data[i].high - prev_close),
                abs(data[i].low - prev_close)
            )
        trs.append(tr)
        running_sum += tr

        if i >= period:
            running_sum -= trs[i - period]  # 直接减去最旧的TR值
            atr_values[i] = running_sum / Decimal(period)

    return atr_values


def calculate_zigzag(
    data: List[StockData],
    atr_multiplier: Decimal = Decimal('1.5'),
    atr_period: int = 14,
    min_price_move: Decimal = Decimal('0'),    # 额外最小转折距离
    min_trend_bars: int = 1                    # 反转前最少趋势持续bar数
):
    if not data:
        return

    atr_values = calculate_atr(data, atr_period)

    high_val = data[0].high
    high_idx = 0
    low_val = data[0].low
    low_idx = 0

    trend: Optional[str] = None
    trend_start_idx = 0

    for i in range(1, len(data)):
        atr = atr_values[i]

        if data[i].date == datetime(2023, 3, 13):
            print(f"atr: {atr}")

        # 跳过平盘bar（高低价等于前收盘）
        if data[i].high == data[i].low == data[i-1].close:
            continue
        
        # 更新当前趋势极值
        if data[i].high > high_val:
            high_val = data[i].high
            high_idx = i
        if data[i].low < low_val:
            low_val = data[i].low
            low_idx = i

        if atr is None:
            continue

        # 初始趋势判断
        if trend is None:
            if (high_val - low_val) >= atr * atr_multiplier and (high_val - low_val) >= min_price_move:
                if high_idx > low_idx:
                    pivot_idx = low_idx
                    data[pivot_idx].is_zigzag_point = True
                    data[pivot_idx].zigzag_point_type = 'LOW'
                    trend = 'UP'
                else:
                    pivot_idx = high_idx
                    data[pivot_idx].is_zigzag_point = True
                    data[pivot_idx].zigzag_point_type = 'HIGH'
                    trend = 'DOWN'
                trend_start_idx = i
                # 重置两方向极值
                high_val = data[i].high
                high_idx = i
                low_val = data[i].low
                low_idx = i
            continue

        # 已有趋势，检测反转
        bars_in_trend = i - max(high_idx, low_idx)
        if trend == 'UP':
            if (high_val - data[i].low) >= atr * atr_multiplier and \
               (high_val - data[i].low) >= min_price_move and \
               bars_in_trend >= min_trend_bars:
                # 标记上升趋势的顶点
                data[high_idx].is_zigzag_point = True
                data[high_idx].zigzag_point_type = 'HIGH'
                trend = 'DOWN'
                trend_start_idx = i
                # 重置两方向极值
                high_val = data[i].high
                high_idx = i
                low_val = data[i].low
                low_idx = i

        elif trend == 'DOWN':
            if (data[i].high - low_val) >= atr * atr_multiplier and \
               (data[i].high - low_val) >= min_price_move and \
               bars_in_trend >= min_trend_bars:
                # 标记下降趋势的谷底
                data[low_idx].is_zigzag_point = True
                data[low_idx].zigzag_point_type = 'LOW'
                trend = 'UP'
                trend_start_idx = i
                # 重置两方向极值
                high_val = data[i].high
                high_idx = i
                low_val = data[i].low
                low_idx = i

    # 末尾补最后一个极值
    if trend == 'UP':
        data[high_idx].is_zigzag_point = True
        data[high_idx].zigzag_point_type = 'HIGH'
    elif trend == 'DOWN':
        data[low_idx].is_zigzag_point = True
        data[low_idx].zigzag_point_type = 'LOW'


def calculate_zigzag1(
    data: List[StockData],
    atr_multiplier: Decimal = Decimal('1.5'),
    atr_period: int = 14,
    min_price_move: Decimal = Decimal('0'),    
    min_trend_bars: int = 1,                   
    lookback_bars: int = 5                    
):
    if not data:
        return

    atr_values = calculate_atr(data, atr_period)

    # 初始化
    trend: Optional[str] = None
    trend_start_idx = 0
    high_val = data[0].high
    high_idx = 0
    low_val = data[0].low
    low_idx = 0

    def is_local_extreme(idx: int, point_type: str) -> bool:
        """检查是否是局部极值"""
        start = max(0, idx - lookback_bars)
        end = min(len(data), idx + lookback_bars + 1)
        if point_type == 'HIGH':
            return all(data[idx].high >= data[j].high for j in range(start, end))
        else:
            return all(data[idx].low <= data[j].low for j in range(start, end))

    def add_zigzag_point(idx: int, point_type: str):
        """避免连续的 HIGH 或 LOW 点，自动替换弱点"""
        last_point = next((d for d in reversed(data[:idx]) if getattr(d, "is_zigzag_point", False)), None)

        if last_point and last_point.zigzag_point_type.startswith(point_type):
            # 已经有相同类型的点 -> 保留更极端的
            if point_type == 'HIGH' and data[idx].high > last_point.high:
                last_point.is_zigzag_point = False
                data[idx].is_zigzag_point = True
                data[idx].zigzag_point_type = point_type
            elif point_type == 'LOW' and data[idx].low < last_point.low:
                last_point.is_zigzag_point = False
                data[idx].is_zigzag_point = True
                data[idx].zigzag_point_type = point_type
            # 否则忽略当前点
        else:
            data[idx].is_zigzag_point = True
            data[idx].zigzag_point_type = point_type

    for i in range(1, len(data)):
        atr = atr_values[i]
        if atr is None:
            continue

        # 初始化趋势
        if trend is None:
            if data[i].high > high_val:
                high_val = data[i].high
                high_idx = i
            if data[i].low < low_val:
                low_val = data[i].low
                low_idx = i

            if (high_val - low_val) >= atr * atr_multiplier and (high_val - low_val) >= min_price_move:
                if high_val - data[i].close > data[i].close - low_val:
                    trend = 'DOWN'
                    if is_local_extreme(high_idx, 'HIGH'):
                        add_zigzag_point(high_idx, 'HIGH')
                else:
                    trend = 'UP'
                    if is_local_extreme(low_idx, 'LOW'):
                        add_zigzag_point(low_idx, 'LOW')
                trend_start_idx = i
            continue

        bars_in_trend = i - trend_start_idx

        # 更新趋势内极值
        if trend == 'UP':
            if data[i].high > high_val:
                high_val = data[i].high
                high_idx = i
        elif trend == 'DOWN':
            if data[i].low < low_val:
                low_val = data[i].low
                low_idx = i

        # 反转逻辑
        if trend == 'UP':
            if (high_val - data[i].low) >= atr * atr_multiplier and \
               (high_val - data[i].low) >= min_price_move and \
               bars_in_trend >= min_trend_bars:
                if is_local_extreme(high_idx, 'HIGH'):
                    add_zigzag_point(high_idx, 'HIGH')
                trend = 'DOWN'
                trend_start_idx = i
                low_val = data[i].low
                low_idx = i

        elif trend == 'DOWN':
            if (data[i].high - low_val) >= atr * atr_multiplier and \
               (data[i].high - low_val) >= min_price_move and \
               bars_in_trend >= min_trend_bars:
                if is_local_extreme(low_idx, 'LOW'):
                    add_zigzag_point(low_idx, 'LOW')
                trend = 'UP'
                trend_start_idx = i
                high_val = data[i].high
                high_idx = i

    # 末尾未确认点
    if trend == 'UP':
        if is_local_extreme(high_idx, 'HIGH'):
            add_zigzag_point(high_idx, 'PENDING_HIGH')
    elif trend == 'DOWN':
        if is_local_extreme(low_idx, 'LOW'):
            add_zigzag_point(low_idx, 'PENDING_LOW')


def validate_zigzag_points(data: List[StockData], window: int = 5):
    """
    验证ZigZag点是否确实是局部极值
    """
    print("\n=== 验证ZigZag点 ===")
    for i, point in enumerate(data):
        if point.is_zigzag_point:
            # 检查窗口范围内是否确实是极值
            start_idx = max(0, i - window)
            end_idx = min(len(data), i + window + 1)
            
            if point.zigzag_point_type == 'HIGH':
                window_highs = [data[j].high for j in range(start_idx, end_idx)]
                is_valid = point.high == max(window_highs)
                print(f"索引{i} HIGH点 {point.high}: {'✓' if is_valid else '✗'} "
                      f"(窗口{start_idx}-{end_idx-1}最高{max(window_highs)})")
                
            elif point.zigzag_point_type == 'LOW':
                window_lows = [data[j].low for j in range(start_idx, end_idx)]
                is_valid = point.low == min(window_lows)
                print(f"索引{i} LOW点 {point.low}: {'✓' if is_valid else '✗'} "
                      f"(窗口{start_idx}-{end_idx-1}最低{min(window_lows)})")



stock_code = '002324'
start_date ='20090101'
end_date = '20250820'
df = get_stock_data(stock_code, start_date, end_date)
stock_data_list = dataframe_to_stock_data(df, stock_code)
calculate_zigzag1(stock_data_list, atr_multiplier=Decimal('1.8'))

print("验证结果...")
validate_zigzag_points(stock_data_list)

print("正在生成可视化图表...")
    
# 创建可视化
visualizer = StockVisualizer()
output_file = f"{stock_code}_analysis_{start_date}_{end_date}.html"
filename = visualizer.save_chart(stock_data_list, output_file, stock_code)