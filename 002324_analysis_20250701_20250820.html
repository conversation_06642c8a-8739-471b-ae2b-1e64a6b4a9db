<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="7a19f8b99ba14b79a104f7042da69487" class="chart-container" style="width:100%; height:800px; "></div>
    <script>
        var chart_7a19f8b99ba14b79a104f7042da69487 = echarts.init(
            document.getElementById('7a19f8b99ba14b79a104f7042da69487'), 'white', {renderer: 'canvas'});
            window.stockDataForTooltip = [{"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 558877}, {"trend_status": "暂无", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 330304}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 186955}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 140648}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 561649}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 809595}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 451048}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 610952}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 309063}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 257664}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 339131}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 321934}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 364453}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 248890}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 244338}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 210119}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 182009}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 284838}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 178417}, {"trend_status": "UP", "is_zigzag_point": true, "zigzag_point_type": "HIGH", "volume": 288924}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 154047}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 213764}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 184134}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 129997}, {"trend_status": "DOWN", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 135854}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 359069}, {"trend_status": "DOWN", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 422837}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 229566}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 244341}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 248100}, {"trend_status": "暂无", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 239121}, {"trend_status": "DOWN", "is_zigzag_point": true, "zigzag_point_type": "LOW", "volume": 620831}, {"trend_status": "DOWN", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 312995}, {"trend_status": "DOWN", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 332146}, {"trend_status": "DOWN", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 324804}, {"trend_status": "DOWN", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 257526}, {"trend_status": "DOWN", "is_zigzag_point": false, "zigzag_point_type": null, "volume": 176181}]
            window.zigzagValidation = {"total_points": 5, "zigzag_points": [{"index": 1, "date": "2025-07-02", "type": "LOW", "price": 10.37, "close": 10.69}, {"index": 19, "date": "2025-07-28", "type": "HIGH", "price": 12.94, "close": 12.62}, {"index": 24, "date": "2025-08-04", "type": "LOW", "price": 11.77, "close": 12.13}, {"index": 26, "date": "2025-08-06", "type": "LOW", "price": 12.69, "close": 13.12}, {"index": 31, "date": "2025-08-13", "type": "LOW", "price": 13.25, "close": 14.25}], "consecutive_issues": [{"point1": {"index": 24, "date": "2025-08-04", "type": "LOW", "price": 11.77, "close": 12.13}, "point2": {"index": 26, "date": "2025-08-06", "type": "LOW", "price": 12.69, "close": 13.12}, "missing_type": "HIGH", "suggested_point": {"index": 25, "date": "2025-08-05", "type": "HIGH", "price": 12.99}}, {"point1": {"index": 26, "date": "2025-08-06", "type": "LOW", "price": 12.69, "close": 13.12}, "point2": {"index": 31, "date": "2025-08-13", "type": "LOW", "price": 13.25, "close": 14.25}, "missing_type": "HIGH", "suggested_point": {"index": 30, "date": "2025-08-12", "type": "HIGH", "price": 13.47}}], "is_valid": false}
            document.addEventListener('DOMContentLoaded', function() { 
            var validationPanel = document.createElement('div');
            validationPanel.id = 'zigzag-validation-panel';
            validationPanel.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                width: 350px;
                background: #fff3cd;
                border: 2px solid #ffeaa7;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                max-height: 400px;
                overflow-y: auto;
            `;
        
            validationPanel.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h3 style="margin: 0; color: #856404;">⚠️ ZigZag 序列问题检测</h3>
                    <button onclick="this.parentElement.parentElement.style.display='none'"
                            style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>股票:</strong> 002324<br>
                    <strong>ZigZag 点总数:</strong> 5<br>
                    <strong>发现问题:</strong> 2 个连续性问题
                </div>
        
                <div style="margin-top: 15px;">
                    <strong style="color: #721c24;">问题详情:</strong>
                    <div style="margin-top: 8px;">
            
                    <div style="background: #f8d7da; padding: 8px; margin: 5px 0; border-radius: 4px; border-left: 4px solid #dc3545;">
                        <div style="font-weight: bold;">问题 1: 连续的 LOW 点</div>
                        <div style="font-size: 12px; margin-top: 4px;">
                            • 2025-08-04 (LOW: 11.77)<br>
                            • 2025-08-06 (LOW: 12.69)<br>
                            <span style="color: #721c24;">缺失: HIGH 点</span>
                
                            <br><span style="color: #155724;">建议: 2025-08-05 (HIGH: 12.99)</span>
                    
                        </div>
                    </div>
                
                    <div style="background: #f8d7da; padding: 8px; margin: 5px 0; border-radius: 4px; border-left: 4px solid #dc3545;">
                        <div style="font-weight: bold;">问题 2: 连续的 LOW 点</div>
                        <div style="font-size: 12px; margin-top: 4px;">
                            • 2025-08-06 (LOW: 12.69)<br>
                            • 2025-08-13 (LOW: 13.25)<br>
                            <span style="color: #721c24;">缺失: HIGH 点</span>
                
                            <br><span style="color: #155724;">建议: 2025-08-12 (HIGH: 13.47)</span>
                    
                        </div>
                    </div>
                </div></div>
                <div style="margin-top: 15px; padding: 8px; background: #d1ecf1; border-radius: 4px; font-size: 12px;">
                    <strong>说明:</strong> ZigZag 点应该是 HIGH 和 LOW 交替出现的。连续的同类型点表明算法可能存在问题或数据异常。
                </div>
            `;
            document.body.appendChild(validationPanel);
         });
        var option_7a19f8b99ba14b79a104f7042da69487 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    11.18,
                    10.78,
                    10.76,
                    11.45
                ],
                [
                    10.64,
                    10.69,
                    10.37,
                    10.83
                ],
                [
                    10.61,
                    10.58,
                    10.46,
                    10.7
                ],
                [
                    10.59,
                    10.41,
                    10.4,
                    10.64
                ],
                [
                    10.8,
                    11.45,
                    10.7,
                    11.45
                ],
                [
                    11.64,
                    11.51,
                    11.33,
                    11.77
                ],
                [
                    11.6,
                    11.54,
                    11.4,
                    11.79
                ],
                [
                    11.49,
                    11.83,
                    11.29,
                    11.96
                ],
                [
                    11.83,
                    11.6,
                    11.54,
                    11.89
                ],
                [
                    11.52,
                    11.68,
                    11.43,
                    11.82
                ],
                [
                    11.6,
                    11.89,
                    11.57,
                    12.0
                ],
                [
                    11.97,
                    11.89,
                    11.81,
                    12.18
                ],
                [
                    11.89,
                    12.25,
                    11.82,
                    12.43
                ],
                [
                    12.21,
                    12.15,
                    12.12,
                    12.48
                ],
                [
                    12.25,
                    12.26,
                    11.94,
                    12.3
                ],
                [
                    12.16,
                    12.18,
                    12.01,
                    12.23
                ],
                [
                    12.13,
                    12.1,
                    11.99,
                    12.21
                ],
                [
                    12.28,
                    12.42,
                    12.08,
                    12.55
                ],
                [
                    12.39,
                    12.18,
                    12.15,
                    12.49
                ],
                [
                    12.11,
                    12.62,
                    12.1,
                    12.94
                ],
                [
                    12.53,
                    12.54,
                    12.43,
                    12.68
                ],
                [
                    12.47,
                    12.12,
                    12.08,
                    12.54
                ],
                [
                    12.12,
                    12.0,
                    11.97,
                    12.27
                ],
                [
                    12.08,
                    11.87,
                    11.83,
                    12.09
                ],
                [
                    11.79,
                    12.13,
                    11.77,
                    12.2
                ],
                [
                    12.33,
                    12.84,
                    12.31,
                    12.99
                ],
                [
                    12.79,
                    13.12,
                    12.69,
                    13.69
                ],
                [
                    13.12,
                    12.89,
                    12.83,
                    13.19
                ],
                [
                    13.08,
                    13.05,
                    12.94,
                    13.2
                ],
                [
                    13.11,
                    13.17,
                    13.06,
                    13.31
                ],
                [
                    13.22,
                    13.31,
                    13.0,
                    13.47
                ],
                [
                    13.27,
                    14.25,
                    13.25,
                    14.58
                ],
                [
                    14.15,
                    13.79,
                    13.68,
                    14.2
                ],
                [
                    13.77,
                    14.25,
                    13.73,
                    14.44
                ],
                [
                    14.38,
                    14.15,
                    14.09,
                    14.5
                ],
                [
                    14.15,
                    14.13,
                    13.87,
                    14.33
                ],
                [
                    14.0,
                    14.2,
                    13.83,
                    14.2
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#ec0000",
                "borderColor0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "ZigZag\u8d8b\u52bf\u7ebf",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    10.37
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-28",
                    12.94
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-04",
                    11.77
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    12.69
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    13.25
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "dashed",
                "color": "#1890ff"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u5cf0\u70b9",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 14,
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-28",
                    12.94
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff6b6b",
                "borderColor": "#ffffff",
                "borderWidth": 1
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u5cf0\u70b9\u8fde\u7ebf",
            "connectNulls": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    null
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-28",
                    12.94
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-04",
                    null
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    null
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    null
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff6b6b"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u8c37\u70b9",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 14,
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    10.37
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-04",
                    11.77
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    12.69
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    13.25
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4ecdc4",
                "borderColor": "#ffffff",
                "borderWidth": 1
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u8c37\u70b9\u8fde\u7ebf",
            "connectNulls": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "none",
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-07-01",
                    null
                ],
                [
                    "2025-07-02",
                    10.37
                ],
                [
                    "2025-07-03",
                    null
                ],
                [
                    "2025-07-04",
                    null
                ],
                [
                    "2025-07-07",
                    null
                ],
                [
                    "2025-07-08",
                    null
                ],
                [
                    "2025-07-09",
                    null
                ],
                [
                    "2025-07-10",
                    null
                ],
                [
                    "2025-07-11",
                    null
                ],
                [
                    "2025-07-14",
                    null
                ],
                [
                    "2025-07-15",
                    null
                ],
                [
                    "2025-07-16",
                    null
                ],
                [
                    "2025-07-17",
                    null
                ],
                [
                    "2025-07-18",
                    null
                ],
                [
                    "2025-07-21",
                    null
                ],
                [
                    "2025-07-22",
                    null
                ],
                [
                    "2025-07-23",
                    null
                ],
                [
                    "2025-07-24",
                    null
                ],
                [
                    "2025-07-25",
                    null
                ],
                [
                    "2025-07-28",
                    null
                ],
                [
                    "2025-07-29",
                    null
                ],
                [
                    "2025-07-30",
                    null
                ],
                [
                    "2025-07-31",
                    null
                ],
                [
                    "2025-08-01",
                    null
                ],
                [
                    "2025-08-04",
                    11.77
                ],
                [
                    "2025-08-05",
                    null
                ],
                [
                    "2025-08-06",
                    12.69
                ],
                [
                    "2025-08-07",
                    null
                ],
                [
                    "2025-08-08",
                    null
                ],
                [
                    "2025-08-11",
                    null
                ],
                [
                    "2025-08-12",
                    null
                ],
                [
                    "2025-08-13",
                    13.25
                ],
                [
                    "2025-08-14",
                    null
                ],
                [
                    "2025-08-15",
                    null
                ],
                [
                    "2025-08-18",
                    null
                ],
                [
                    "2025-08-19",
                    null
                ],
                [
                    "2025-08-20",
                    null
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1.5,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#4ecdc4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                {
                    "value": 558877,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 330304,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 186955,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 140648,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 561649,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 809595,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 451048,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 610952,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 309063,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 257664,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 339131,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 321934,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 364453,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 248890,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 244338,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 210119,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 182009,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 284838,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 178417,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 288924,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 154047,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 213764,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 184134,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 129997,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 135854,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 359069,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 422837,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 229566,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 244341,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 248100,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 239121,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 620831,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 312995,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 332146,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                },
                {
                    "value": 324804,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 257526,
                    "itemStyle": {
                        "color": "#00da3c40"
                    }
                },
                {
                    "value": 176181,
                    "itemStyle": {
                        "color": "#ec000040"
                    }
                }
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "ZigZag\u8d8b\u52bf\u7ebf",
                "\u5cf0\u70b9",
                "\u5cf0\u70b9\u8fde\u7ebf",
                "\u8c37\u70b9",
                "\u8c37\u70b9\u8fde\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter":                         function (params) {                            var k = null;                            for (var i = 0; i < params.length; i++) {                                if (params[i].seriesType === 'candlestick') { k = params[i]; break; }                            }                            if (!k && params.length) { k = params[0]; }                            if (!k) { return ''; }                            var idx = k.dataIndex || 0;                            var sdArr = window.stockDataForTooltip || [];                            var sd = sdArr[idx] || {};                            var v = Array.isArray(k.value) ? k.value : [];                            function num(n){ return (n == null || isNaN(n)) ? '-' : Number(n).toFixed(2); }                            var open = num(v[1]);                            var close = num(v[2]);                            var low = num(v[3]);                            var high = num(v[4]);                            var parts = [];                            parts.push('\u65e5\u671f: ' + (k.axisValue || ''));                            parts.push('\u5f00\u76d8: ' + open);                            parts.push('\u6536\u76d8: ' + close);                            parts.push('\u6700\u4f4e: ' + low);                            parts.push('\u6700\u9ad8: ' + high);                            parts.push('\u6210\u4ea4\u91cf: ' + (sd.volume != null ? Number(sd.volume).toLocaleString() : '-'));                            if (sd.is_zigzag_point) { parts.push('\u5cf0\u8c37\u70b9: ' + (sd.zigzag_point_type || '')); }                            parts.push('\u8d8b\u52bf\u72b6\u6001: ' + (sd.trend_status || '\u6682\u65e0'));                            return parts.join('<br/>');                        }                    ,
        "textStyle": {
            "color": "#000",
            "fontSize": 12
        },
        "backgroundColor": "rgba(245, 245, 245, 0.95)",
        "borderColor": "#ccc",
        "borderWidth": 1,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 20,
            "boundaryGap": false,
            "min": "dataMin",
            "max": "dataMax",
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29",
                "2025-07-30",
                "2025-07-31",
                "2025-08-01",
                "2025-08-04",
                "2025-08-05",
                "2025-08-06",
                "2025-08-07",
                "2025-08-08",
                "2025-08-11",
                "2025-08-12",
                "2025-08-13",
                "2025-08-14",
                "2025-08-15",
                "2025-08-18",
                "2025-08-19",
                "2025-08-20"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29",
                "2025-07-30",
                "2025-07-31",
                "2025-08-01",
                "2025-08-04",
                "2025-08-05",
                "2025-08-06",
                "2025-08-07",
                "2025-08-08",
                "2025-08-11",
                "2025-08-12",
                "2025-08-13",
                "2025-08-14",
                "2025-08-15",
                "2025-08-18",
                "2025-08-19",
                "2025-08-20"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 3,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u80a1\u7968K\u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "left": "0",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": false,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "top": "90%",
            "filterMode": "filter"
        },
        [
            {
                "show": false,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "top": "90%",
                "filterMode": "filter"
            }
        ]
    ],
    "axisPointer": {
        "show": true,
        "type": "line",
        "link": [
            {
                "xAxisIndex": "all"
            }
        ],
        "label": {
            "show": true,
            "margin": 8,
            "backgroundColor": "#777",
            "valueAnimation": false
        },
        "triggerTooltip": true,
        "triggerOn": "mousemove|click"
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "right": "8%",
            "height": "60%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "16%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_7a19f8b99ba14b79a104f7042da69487.setOption(option_7a19f8b99ba14b79a104f7042da69487);
            window.addEventListener('resize', function(){
                chart_7a19f8b99ba14b79a104f7042da69487.resize();
            })
    </script>
</body>
</html>
