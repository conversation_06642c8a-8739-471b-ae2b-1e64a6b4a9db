#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataclasses import dataclass
from decimal import Decimal
from typing import Optional, List
from datetime import datetime, timedelta

@dataclass
class StockData:
    symbol: str
    date: datetime
    high: Decimal
    low: Decimal
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None
    trend_status: Optional[str] = None

@dataclass
class ZigZagPoint:
    date: str
    point_type: str
    price: Decimal

@dataclass
class ZigZagState:
    symbol: str
    confirmed_points: List[ZigZagPoint] = None

    def __post_init__(self):
        if self.confirmed_points is None:
            self.confirmed_points = []

def _clear_zigzag_point_marking(stock_data_list: List[StockData], date: str, symbol: str):
    """清除指定日期的zigzag点标记"""
    for stock_data in stock_data_list:
        stock_date_str = stock_data.date.strftime('%Y-%m-%d')
        if stock_date_str == date and stock_data.symbol == symbol:
            stock_data.is_zigzag_point = False
            stock_data.zigzag_point_type = None
            print(f"    清除旧标记: {date}")
            break

def _confirm_point(state: ZigZagState, point_type: str, date: str, price: Decimal, stock_data_list: List[StockData] = None):
    """确认拐点，避免连续的高点或低点"""
    print(f"  尝试确认{point_type}点: {date} {price}")
    
    # 检查是否会产生连续的同类型点
    if state.confirmed_points and state.confirmed_points[-1].point_type == point_type:
        last = state.confirmed_points[-1]
        print(f"    发现连续{point_type}点，当前最强: {last.date} {last.price}")
        
        should_replace = False
        if point_type == "HIGH":
            should_replace = price > last.price
        else:  # LOW
            should_replace = price < last.price
        
        if should_replace:
            # 先清除旧点的标记
            if stock_data_list:
                _clear_zigzag_point_marking(stock_data_list, last.date, state.symbol)
            
            # 更新为更极端的点
            state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
            print(f"    ✅ 替换为更极端的{point_type}点: {last.date} {last.price} -> {date} {price}")
        else:
            # 不替换，直接返回
            print(f"    ❌ 跳过较弱的{point_type}点: {date} {price}")
            return
    else:
        # 添加新的拐点
        state.confirmed_points.append(ZigZagPoint(date, point_type, price))
        print(f"    ✅ 确认新{point_type}点: {date} {price}")

    # 更新对应的 StockData 对象
    if stock_data_list:
        for stock_data in stock_data_list:
            stock_date_str = stock_data.date.strftime('%Y-%m-%d')
            if stock_date_str == date and stock_data.symbol == state.symbol:
                stock_data.is_zigzag_point = True
                stock_data.zigzag_point_type = point_type
                print(f"    标记StockData: {date} {point_type}")
                break

def test_consecutive_points():
    """测试连续高点/低点的处理"""
    print("=== 测试连续高点/低点处理 ===")
    
    # 创建测试数据
    base_date = datetime(2025, 7, 1)
    stock_data_list = []
    
    # 模拟连续高点的情况
    test_data = [
        ("2025-07-01", 10.0, 11.0),  # 第一个高点
        ("2025-07-02", 9.5, 11.5),   # 更高的高点（应该替换第一个）
        ("2025-07-03", 9.0, 11.2),   # 较低的高点（应该被跳过）
        ("2025-07-04", 8.5, 8.0),    # 第一个低点
        ("2025-07-05", 7.5, 7.0),    # 更低的低点（应该替换第一个）
        ("2025-07-06", 8.0, 7.5),    # 较高的低点（应该被跳过）
    ]
    
    for i, (date_str, low, high) in enumerate(test_data):
        date = datetime.strptime(date_str, '%Y-%m-%d')
        stock_data = StockData(
            symbol="TEST001",
            date=date,
            high=Decimal(str(high)),
            low=Decimal(str(low))
        )
        stock_data_list.append(stock_data)
    
    # 初始化状态
    state = ZigZagState(symbol="TEST001")
    
    # 模拟确认拐点的过程
    print("\n开始确认拐点...")
    
    # 确认第一个高点
    _confirm_point(state, "HIGH", "2025-07-01", Decimal("11.0"), stock_data_list)
    
    # 尝试确认更高的高点（应该替换）
    _confirm_point(state, "HIGH", "2025-07-02", Decimal("11.5"), stock_data_list)
    
    # 尝试确认较低的高点（应该被跳过）
    _confirm_point(state, "HIGH", "2025-07-03", Decimal("11.2"), stock_data_list)
    
    # 确认第一个低点
    _confirm_point(state, "LOW", "2025-07-04", Decimal("8.0"), stock_data_list)
    
    # 尝试确认更低的低点（应该替换）
    _confirm_point(state, "LOW", "2025-07-05", Decimal("7.0"), stock_data_list)
    
    # 尝试确认较高的低点（应该被跳过）
    _confirm_point(state, "LOW", "2025-07-06", Decimal("7.5"), stock_data_list)
    
    print(f"\n=== 最终结果 ===")
    print(f"总确认拐点数: {len(state.confirmed_points)}")
    for i, point in enumerate(state.confirmed_points):
        print(f"拐点{i+1}: {point.date} {point.point_type} {point.price}")
    
    # 验证没有连续的同类型点
    consecutive_count = 0
    for i in range(1, len(state.confirmed_points)):
        if state.confirmed_points[i].point_type == state.confirmed_points[i-1].point_type:
            consecutive_count += 1
            print(f"⚠️  发现连续同类型点: {state.confirmed_points[i-1].point_type} -> {state.confirmed_points[i].point_type}")
    
    if consecutive_count == 0:
        print("✅ 没有连续的同类型点，优化成功！")
    else:
        print(f"❌ 发现 {consecutive_count} 个连续同类型点")
    
    print("\n=== StockData标记情况 ===")
    for i, data in enumerate(stock_data_list):
        if data.is_zigzag_point:
            price = data.high if data.zigzag_point_type == 'HIGH' else data.low
            print(f"{data.date.strftime('%Y-%m-%d')}: {data.zigzag_point_type} {price}")
        else:
            print(f"{data.date.strftime('%Y-%m-%d')}: 无标记")
    
    # 验证期望结果
    expected_points = [
        ("2025-07-02", "HIGH", Decimal("11.5")),  # 最高的高点
        ("2025-07-05", "LOW", Decimal("7.0"))     # 最低的低点
    ]
    
    print(f"\n=== 验证期望结果 ===")
    if len(state.confirmed_points) == len(expected_points):
        all_correct = True
        for i, (exp_date, exp_type, exp_price) in enumerate(expected_points):
            actual = state.confirmed_points[i]
            if actual.date == exp_date and actual.point_type == exp_type and actual.price == exp_price:
                print(f"✅ 拐点{i+1}正确: {exp_date} {exp_type} {exp_price}")
            else:
                print(f"❌ 拐点{i+1}错误: 期望{exp_date} {exp_type} {exp_price}, 实际{actual.date} {actual.point_type} {actual.price}")
                all_correct = False
        
        if all_correct:
            print("🎉 所有拐点都符合期望！")
        else:
            print("⚠️  部分拐点不符合期望")
    else:
        print(f"❌ 拐点数量错误: 期望{len(expected_points)}, 实际{len(state.confirmed_points)}")

if __name__ == "__main__":
    test_consecutive_points()
